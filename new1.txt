index.vue:488  知识库服务连接失败: AxiosError {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
checkTokenStatus @ index.vue:488
await in checkTokenStatus
(匿名) @ index.vue:448
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6263
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
(匿名) @ vue-router.mjs:3351
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
(匿名) @ main.ts:28
knowledgeBase.ts:232   GET http://192.168.66.13:8000/api/iot/v1/knowledge-base/health net::ERR_CONNECTION_TIMED_OUT
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
wrap @ bind.js:5
checkKnowledgeBaseHealth @ knowledgeBase.ts:232
checkTokenStatus @ index.vue:478
(匿名) @ index.vue:448
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6263
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
(匿名) @ vue-router.mjs:3351
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
(匿名) @ main.ts:28
index.vue:523  加载知识库列表失败: AxiosError {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
loadKnowledgeBases @ index.vue:523
await in loadKnowledgeBases
(匿名) @ index.vue:450
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6263
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
(匿名) @ vue-router.mjs:3351
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
(匿名) @ main.ts:28
knowledgeBase.ts:179   GET http://192.168.66.13:8000/api/iot/v1/knowledge-base/list?page=1&page_size=30&orderby=create_time&desc=true net::ERR_CONNECTION_TIMED_OUT
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
wrap @ bind.js:5
getKnowledgeBaseList @ knowledgeBase.ts:179
loadKnowledgeBases @ index.vue:511
(匿名) @ index.vue:450
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6263
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
(匿名) @ vue-router.mjs:3351
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
(匿名) @ main.ts:28
index.vue:549  加载统计信息失败: AxiosError {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
loadStats @ index.vue:549
await in loadStats
(匿名) @ index.vue:451
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
processFragment @ runtime-core.esm-bundler.js:5143
patch @ runtime-core.esm-bundler.js:4703
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
(匿名) @ runtime-core.esm-bundler.js:2512
Promise.then
setup @ runtime-core.esm-bundler.js:2511
callWithErrorHandling @ runtime-core.esm-bundler.js:199
setupStatefulComponent @ runtime-core.esm-bundler.js:7965
setupComponent @ runtime-core.esm-bundler.js:7926
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6263
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
(匿名) @ vue-router.mjs:3351
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
(匿名) @ main.ts:28
knowledgeBase.ts:222   GET http://192.168.66.13:8000/api/iot/v1/knowledge-base/stats/overview net::ERR_CONNECTION_TIMED_OUT