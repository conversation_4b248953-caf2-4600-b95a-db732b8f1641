.elx-highlight-code-wrapper {
  display: flex;
  background: transparent;
  overflow: hidden;

  .line-numbers {
    -webkit-user-select: none;
    user-select: none;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-right: 1rem;
    .line-number {
      display: inline-block;
      text-align: right;
      padding: 0 0 0 0.3em;
      -webkit-user-select: none;
      user-select: none;
      flex-shrink: 0;
      color: var(--el-text-color-secondary);
    }
  }

  .elx-highlight-code-scrollbar {
    .code-lines {
      white-space: pre;
      & > span {
        width: max-content;
        display: block;
        .line {
          width: max-content;
          display: inline-block;
          white-space: pre;
          span {
            display: inline-block;
          }
        }
      }
    }
  }
}
