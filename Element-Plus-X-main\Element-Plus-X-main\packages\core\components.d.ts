/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Attachments: typeof import('./src/components/Attachments/index.vue')['default']
    Audio: typeof import('./src/components/FilesCard/fileSvg/audio.vue')['default']
    Bubble: typeof import('./src/components/Bubble/index.vue')['default']
    BubbleList: typeof import('./src/components/BubbleList/index.vue')['default']
    ClearButton: typeof import('./src/components/EditorSender/components/ClearButton/index.vue')['default']
    Code: typeof import('./src/components/FilesCard/fileSvg/code.vue')['default']
    CodeBlock: typeof import('./src/components/XMarkdownCore/components/CodeBlock/index.vue')['default']
    CodeLine: typeof import('./src/components/XMarkdownCore/components/CodeLine/index.vue')['default']
    CodeX: typeof import('./src/components/XMarkdownCore/components/CodeX/index.vue')['default']
    ConfigProvider: typeof import('./src/components/ConfigProvider/index.vue')['default']
    Conversations: typeof import('./src/components/Conversations/index.vue')['default']
    CopyCodeButton: typeof import('./src/components/XMarkdownCore/components/CodeBlock/copy-code-button.vue')['default']
    CustomLoading: typeof import('./src/components/XMarkdownCore/components/RunCode/components/custom-loading.vue')['default']
    Database: typeof import('./src/components/FilesCard/fileSvg/database.vue')['default']
    EditorSender: typeof import('./src/components/EditorSender/index.vue')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMention: typeof import('element-plus/es')['ElMention']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSegmented: typeof import('element-plus/es')['ElSegmented']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Excel: typeof import('./src/components/FilesCard/fileSvg/excel.vue')['default']
    File: typeof import('./src/components/FilesCard/fileSvg/file.vue')['default']
    FilesCard: typeof import('./src/components/FilesCard/index.vue')['default']
    HighLightCode: typeof import('./src/components/XMarkdownCore/components/HighLightCode/index.vue')['default']
    Image: typeof import('./src/components/FilesCard/fileSvg/image.vue')['default']
    IndexAttachments: typeof import('./src/components/Attachments/index-attachments.vue')['default']
    IndexFileList: typeof import('./src/components/Attachments/index-file-list.vue')['default']
    IndexOri: typeof import('./src/components/XMarkdownCore/components/CodeBlock/index-ori.vue')['default']
    Item: typeof import('./src/components/Conversations/components/item.vue')['default']
    Link: typeof import('./src/components/FilesCard/fileSvg/link.vue')['default']
    Loading: typeof import('./src/components/BubbleList/loading.vue')['default']
    LoadingButton: typeof import('./src/components/EditorSender/components/LoadingButton/index.vue')['default']
    Mark: typeof import('./src/components/FilesCard/fileSvg/mark.vue')['default']
    MentionSender: typeof import('./src/components/MentionSender/index.vue')['default']
    Mermaid: typeof import('./src/components/XMarkdownCore/components/Mermaid/index.vue')['default']
    MermaidToolbar: typeof import('./src/components/XMarkdownCore/components/Mermaid/MermaidToolbar.vue')['default']
    Pdf: typeof import('./src/components/FilesCard/fileSvg/pdf.vue')['default']
    Ppt: typeof import('./src/components/FilesCard/fileSvg/ppt.vue')['default']
    Prompts: typeof import('./src/components/Prompts/index.vue')['default']
    RunCode: typeof import('./src/components/XMarkdownCore/components/RunCode/index.vue')['default']
    RunCodeButton: typeof import('./src/components/XMarkdownCore/components/CodeBlock/run-code-button.vue')['default']
    RunCodeContent: typeof import('./src/components/XMarkdownCore/components/RunCode/components/run-code-content.vue')['default']
    RunCodeHeader: typeof import('./src/components/XMarkdownCore/components/RunCode/components/run-code-header.vue')['default']
    SendButton: typeof import('./src/components/EditorSender/components/SendButton/index.vue')['default']
    Sender: typeof import('./src/components/Sender/index.vue')['default']
    SpeechButton: typeof import('./src/components/MentionSender/components/SpeechButton/index.vue')['default']
    SpeechLoadingButton: typeof import('./src/components/MentionSender/components/SpeechLoadingButton/index.vue')['default']
    Thinking: typeof import('./src/components/Thinking/index.vue')['default']
    ThoughtChain: typeof import('./src/components/ThoughtChain/index.vue')['default']
    Three: typeof import('./src/components/FilesCard/fileSvg/three.vue')['default']
    Txt: typeof import('./src/components/FilesCard/fileSvg/txt.vue')['default']
    Typewriter: typeof import('./src/components/Typewriter/index.vue')['default']
    Unknown: typeof import('./src/components/FilesCard/fileSvg/unknown.vue')['default']
    Video: typeof import('./src/components/FilesCard/fileSvg/video.vue')['default']
    Welcome: typeof import('./src/components/Welcome/index.vue')['default']
    Word: typeof import('./src/components/FilesCard/fileSvg/word.vue')['default']
    XMarkdown: typeof import('./src/components/XMarkdown/index.vue')['default']
    XMarkdownAsync: typeof import('./src/components/XMarkdownAsync/index.vue')['default']
    Zip: typeof import('./src/components/FilesCard/fileSvg/zip.vue')['default']
  }
}
