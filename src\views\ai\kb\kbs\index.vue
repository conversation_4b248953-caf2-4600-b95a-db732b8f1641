<template>
  <div class="layout-padding">
    <el-card shadow="hover">
      <!-- 页面头部 -->
      <template #header>
        <div class="card-header">
          <div>
            <h2 style="margin: 0; display: flex; align-items: center; gap: 8px;">
              <el-icon><Search /></el-icon>
              知识库检索
            </h2>
            <p style="margin: 8px 0 0 0; color: var(--el-text-color-regular); font-size: 14px;">
              在知识库中搜索相关内容，支持语义检索和关键词匹配
            </p>
          </div>
          <el-space>
            <!-- 检索状态指示器 -->
            <el-tag :type="searchStatusType" size="default">
              <el-icon><Connection /></el-icon>
              {{ searchStatusText }}
            </el-tag>
            <el-button @click="clearHistory" :disabled="searchHistory.length === 0">
              清空历史
            </el-button>
          </el-space>
        </div>
      </template>

      <!-- 检索输入区域 -->
      <el-card style="margin-bottom: 20px;" class="search-card">
        <div class="search-container">
          <!-- 主要搜索输入 -->
          <div class="search-input-container">
            <el-input
              v-model="searchQuery"
              type="textarea"
              :rows="3"
              placeholder="请输入您要搜索的问题或关键词..."
              :maxlength="2000"
              show-word-limit
              clearable
              @keydown.ctrl.enter="handleSearch"
              class="search-input"
            />
            <div class="search-actions">
              <el-button
                type="primary"
                @click="handleSearch"
                :loading="searching"
                :disabled="!searchQuery.trim()"
                size="large"
                class="search-button"
              >
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="toggleAdvanced" :icon="Setting">
                {{ showAdvanced ? '收起' : '高级选项' }}
              </el-button>
            </div>
          </div>

          <!-- 高级选项 -->
          <el-collapse-transition>
            <div v-show="showAdvanced" class="advanced-options">
              <el-divider content-position="left">高级检索选项</el-divider>
              <el-row :gutter="20">
                <el-col :xs="24" :sm="12" :md="8">
                  <el-form-item label="数据集选择">
                    <el-select
                      v-model="searchParams.dataset_ids"
                      multiple
                      placeholder="选择要搜索的知识库"
                      style="width: 100%"
                      :loading="loadingDatasets"
                      @focus="loadDatasets"
                    >
                      <el-option
                        v-for="dataset in availableDatasets"
                        :key="dataset.id"
                        :label="dataset.name"
                        :value="dataset.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8">
                  <el-form-item label="相似度阈值">
                    <el-slider
                      v-model="searchParams.similarity_threshold"
                      :min="0"
                      :max="1"
                      :step="0.1"
                      show-input
                      :show-input-controls="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8">
                  <el-form-item label="向量权重">
                    <el-slider
                      v-model="searchParams.vector_similarity_weight"
                      :min="0"
                      :max="1"
                      :step="0.1"
                      show-input
                      :show-input-controls="false"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :xs="24" :sm="12" :md="8">
                  <el-form-item label="检索数量">
                    <el-input-number
                      v-model="searchParams.page_size"
                      :min="1"
                      :max="100"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8">
                  <el-form-item label="Top-K">
                    <el-input-number
                      v-model="searchParams.top_k"
                      :min="1"
                      :max="10000"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8">
                  <el-form-item>
                    <el-checkbox v-model="searchParams.keyword">启用关键词匹配</el-checkbox>
                    <el-checkbox v-model="searchParams.highlight">后端高亮显示</el-checkbox>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">

              </el-row>
            </div>
          </el-collapse-transition>
        </div>
      </el-card>

      <!-- 搜索历史 -->
      <el-card v-if="searchHistory.length > 0" style="margin-bottom: 20px;" class="history-card">
        <template #header>
          <div style="display: flex; align-items: center; gap: 8px;">
            <el-icon><Clock /></el-icon>
            <span>搜索历史</span>
          </div>
        </template>
        <div class="history-tags">
          <el-tag
            v-for="(item, index) in searchHistory.slice(0, 10)"
            :key="index"
            @click="useHistoryQuery(item)"
            class="history-tag"
            type="info"
            effect="plain"
          >
            {{ item }}
          </el-tag>
        </div>
      </el-card>

      <!-- 搜索结果 -->
      <div v-if="searchResults.length > 0 || searching" class="search-results">
        <el-card>
          <template #header>
            <div class="results-header">
              <div>
                <el-icon><Document /></el-icon>
                搜索结果
                <el-tag v-if="totalResults > 0" type="success" size="small" style="margin-left: 8px;">
                  共找到 {{ totalResults }} 条结果
                </el-tag>
              </div>
              <el-space>
                <el-button @click="exportResults" :disabled="searchResults.length === 0" size="small">
                  导出结果
                </el-button>
              </el-space>
            </div>
          </template>

          <div v-loading="searching" class="results-container">
            <div v-if="searchResults.length === 0 && !searching" class="no-results">
              <el-empty description="暂无搜索结果" />
            </div>
            <div v-else class="results-list">
              <div
                v-for="(result, index) in searchResults"
                :key="result.id"
                class="result-item"
                @click="viewResultDetail(result)"
              >
                <div class="result-header">
                  <div class="result-meta">
                    <el-tag size="small" type="primary">{{ getDatasetName(result.kb_id) }}</el-tag>
                    <el-tag size="small" type="info">相似度: {{ (result.similarity * 100).toFixed(1) }}%</el-tag>
                    <el-tag v-if="result.important_keywords.length > 0" size="small" type="warning">
                      关键词: {{ result.important_keywords.slice(0, 3).join(', ') }}
                    </el-tag>
                  </div>
                  <div class="result-actions">
                    <el-button size="small" type="text" @click.stop="copyContent(result.content)">
                      复制
                    </el-button>
                    <el-button size="small" type="text" @click.stop="viewResultDetail(result)">
                      详情
                    </el-button>
                  </div>
                </div>
                <div class="result-content">
                  <!-- 调试信息 -->
                  <!-- 调试信息（已关闭） -->
                  <div v-if="false" style="font-size: 12px; color: #999; margin-bottom: 5px; background: #f5f5f5; padding: 5px; border-radius: 3px;">
                    调试: highlight={{ !!result.highlight }}, content长度={{ result.content?.length || 0 }}, 搜索词={{ searchQuery }}<br>
                    highlight内容: {{ result.highlight?.substring(0, 100) + '...' || '无' }}
                  </div>

                  <!-- 高亮内容显示 -->
                  <div v-if="result.highlight" v-html="result.highlight" class="highlighted-content"></div>
                  <div v-else class="normal-content" v-html="highlightText(result.content)"></div>
                </div>
                <div class="result-footer">
                  <div class="similarity-scores">
                    <span>向量相似度: {{ (result.vector_similarity * 100).toFixed(1) }}%</span>
                    <span>词项相似度: {{ (result.term_similarity * 100).toFixed(1) }}%</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 分页 -->
            <el-pagination
              v-if="totalResults > searchParams.page_size"
              v-model:current-page="currentPage"
              v-model:page-size="searchParams.page_size"
              :total="totalResults"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 30, 50]"
              background
              style="margin-top: 20px; justify-content: center;"
              @current-change="handlePageChange"
              @size-change="handleSizeChange"
            />
          </div>
        </el-card>
      </div>
    </el-card>

    <!-- 结果详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="检索结果详情"
      width="800px"
      :before-close="closeDetailDialog"
    >
      <div v-if="currentResult" class="result-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="所属知识库">{{ getDatasetName(currentResult.kb_id) }}</el-descriptions-item>
          <el-descriptions-item label="文档ID">{{ currentResult.document_id }}</el-descriptions-item>
          <el-descriptions-item label="块ID">{{ currentResult.id }}</el-descriptions-item>
          <el-descriptions-item label="相似度分数">{{ (currentResult.similarity * 100).toFixed(2) }}%</el-descriptions-item>
          <el-descriptions-item label="向量相似度">{{ (currentResult.vector_similarity * 100).toFixed(2) }}%</el-descriptions-item>
          <el-descriptions-item label="词项相似度">{{ (currentResult.term_similarity * 100).toFixed(2) }}%</el-descriptions-item>
          <el-descriptions-item label="重要关键词" :span="2">
            <el-tag
              v-for="keyword in currentResult.important_keywords"
              :key="keyword"
              size="small"
              style="margin-right: 8px;"
            >
              {{ keyword }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        <el-divider content-position="left">内容详情</el-divider>
        <div class="content-detail">
          <div v-if="currentResult.highlight" v-html="currentResult.highlight" class="highlighted-content"></div>
          <div v-else class="normal-content" v-html="highlightText(currentResult.content)"></div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="copyContent(currentResult?.content || '')">
            复制内容
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="aiKbKbs">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Session } from '/@/utils/storage';
import {
  Search,
  Setting,
  Connection,
  Clock,
  Document,
  ChatDotRound
} from '@element-plus/icons-vue';
import {
  getKnowledgeBaseList,
  searchKnowledge,
  getRetrievalHistory,
  generateAISummary as callAISummaryAPI,
  checkVLLMStatus,
  type KnowledgeBase,
  type KnowledgeRetrievalParams,
  type SearchResult,
  type KnowledgeRetrievalResponse,
  type AISummaryResponse
} from '/@/api/iot/knowledgeBase';
import { aiSummaryUtils } from '/@/utils/aiSummary';

// 使用从 API 文件导入的类型
type SearchParams = KnowledgeRetrievalParams & {
  page: number;
  page_size: number;
  similarity_threshold: number;
  vector_similarity_weight: number;
  top_k: number;
  keyword: boolean;
  highlight: boolean;
};

type SearchResponse = KnowledgeRetrievalResponse;

// 响应式数据
const searching = ref(false);
const loadingDatasets = ref(false);
const searchQuery = ref('');
const showAdvanced = ref(false);
const searchResults = ref<SearchResult[]>([]);
const searchHistory = ref<string[]>([]);
const availableDatasets = ref<KnowledgeBase[]>([]);
const totalResults = ref(0);
const currentPage = ref(1);

// 搜索参数
const searchParams = reactive<SearchParams>({
  question: '',
  dataset_ids: [],
  page: 1,
  page_size: 20,
  similarity_threshold: 0.2,
  vector_similarity_weight: 0.3,
  top_k: 1024,
  keyword: false,
  highlight: true  // 默认开启高亮
});

// 详情对话框
const detailDialogVisible = ref(false);
const currentResult = ref<SearchResult | null>(null);

// 搜索状态
const searchStatus = ref<'idle' | 'searching' | 'success' | 'error'>('idle');
const searchStatusText = computed(() => {
  switch (searchStatus.value) {
    case 'idle': return '就绪';
    case 'searching': return '搜索中...';
    case 'success': return '搜索完成';
    case 'error': return '搜索失败';
    default: return '未知状态';
  }
});
const searchStatusType = computed(() => {
  switch (searchStatus.value) {
    case 'idle': return 'info';
    case 'searching': return 'warning';
    case 'success': return 'success';
    case 'error': return 'danger';
    default: return 'info';
  }
});

// AI总结相关状态
const layoutMode = ref<'split' | 'stacked' | 'tabs'>('split');
const aiSummaryEnabled = ref(true);
const activeTab = ref('results');
const aiSummaryStatus = ref<'idle' | 'generating' | 'success' | 'error'>('idle');
const aiSummaryContent = ref('');
const aiSummaryError = ref('');
const aiSummaryStats = ref<{
  token_usage: { total_tokens: number; prompt_tokens: number; completion_tokens: number };
  model: string;
  processed_chunks: number;
} | null>(null);

// 生命周期
onMounted(() => {
  loadSearchHistory();
  loadDatasets();
});

// 方法定义

/**
 * 加载可用的数据集
 */
const loadDatasets = async () => {
  if (availableDatasets.value.length > 0) return;

  try {
    loadingDatasets.value = true;
    const response = await getKnowledgeBaseList({ page: 1, page_size: 100 });
    const businessData = response.data;

    if (businessData.code === 200 && businessData.data) {
      availableDatasets.value = businessData.data;
    } else {
      ElMessage.error('加载知识库列表失败');
    }
  } catch (error) {
    console.error('加载知识库列表失败:', error);
    ElMessage.error('加载知识库列表失败');
  } finally {
    loadingDatasets.value = false;
  }
};

/**
 * 执行搜索
 */
const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    ElMessage.warning('请输入搜索内容');
    return;
  }

  try {
    searching.value = true;
    searchStatus.value = 'searching';

    // 准备搜索参数
    const params: SearchParams = {
      ...searchParams,
      question: searchQuery.value.trim(),
      page: currentPage.value
    };

    // 如果没有选择数据集，则搜索所有数据集
    if (!params.dataset_ids || params.dataset_ids.length === 0) {
      params.dataset_ids = availableDatasets.value.map(ds => ds.id!);
    }

    // 调用搜索API
    console.log('发送检索请求:', params);
    const response = await callSearchAPI(params);
    const businessData = response.data;
    console.log('检索响应:', businessData);

    if (businessData.code === 200 && businessData.data) {
      const searchData: SearchResponse = businessData.data;
      searchResults.value = searchData.chunks;
      totalResults.value = searchData.total;
      searchStatus.value = 'success';

      // 调试：检查高亮字段
      console.log('检索结果示例:', searchData.chunks[0]);

      // 保存搜索历史
      saveSearchHistory(searchQuery.value.trim());

      ElMessage.success(`搜索完成，找到 ${searchData.total} 条结果`);
    } else {
      searchStatus.value = 'error';
      ElMessage.error(businessData.msg || businessData.message || '搜索失败');
    }
  } catch (error: any) {
    console.error('搜索失败:', error);
    searchStatus.value = 'error';
    if (error.response?.status === 401) {
      ElMessage.error('认证失败，请重新登录');
    } else {
      ElMessage.error('搜索失败，请稍后重试');
    }
  } finally {
    searching.value = false;
  }
};

/**
 * 调用知识库检索API
 */
const callSearchAPI = async (params: SearchParams) => {
  // 转换参数格式以匹配 API 接口
  const apiParams: KnowledgeRetrievalParams = {
    question: params.question,
    dataset_ids: params.dataset_ids,
    document_ids: params.document_ids,
    page: params.page,
    page_size: params.page_size,
    similarity_threshold: params.similarity_threshold,
    vector_similarity_weight: params.vector_similarity_weight,
    top_k: params.top_k,
    keyword: params.keyword,
    highlight: params.highlight
  };

  return await searchKnowledge(apiParams);
};

/**
 * 切换高级选项显示
 */
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value;
};

/**
 * 使用历史查询
 */
const useHistoryQuery = (query: string) => {
  searchQuery.value = query;
  searchParams.question = query;
};

/**
 * 保存搜索历史
 */
const saveSearchHistory = (query: string) => {
  if (!query || searchHistory.value.includes(query)) return;

  searchHistory.value.unshift(query);
  if (searchHistory.value.length > 20) {
    searchHistory.value = searchHistory.value.slice(0, 20);
  }

  localStorage.setItem('kb_search_history', JSON.stringify(searchHistory.value));
};

/**
 * 加载搜索历史
 */
const loadSearchHistory = () => {
  try {
    const history = localStorage.getItem('kb_search_history');
    if (history) {
      searchHistory.value = JSON.parse(history);
    }
  } catch (error) {
    console.error('加载搜索历史失败:', error);
  }
};

/**
 * 清空搜索历史
 */
const clearHistory = async () => {
  try {
    await ElMessageBox.confirm('确定要清空搜索历史吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    searchHistory.value = [];
    localStorage.removeItem('kb_search_history');
    ElMessage.success('搜索历史已清空');
  } catch (error) {
    // 用户取消操作
  }
};

/**
 * 获取数据集名称
 */
const getDatasetName = (datasetId: string): string => {
  const dataset = availableDatasets.value.find(ds => ds.id === datasetId);
  return dataset?.name || datasetId;
};

/**
 * 查看结果详情
 */
const viewResultDetail = (result: SearchResult) => {
  currentResult.value = result;
  detailDialogVisible.value = true;
};

/**
 * 关闭详情对话框
 */
const closeDetailDialog = () => {
  detailDialogVisible.value = false;
  currentResult.value = null;
};

/**
 * 复制内容
 */
const copyContent = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content);
    ElMessage.success('内容已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    ElMessage.error('复制失败');
  }
};

/**
 * 导出搜索结果
 */
const exportResults = () => {
  if (searchResults.value.length === 0) {
    ElMessage.warning('没有可导出的结果');
    return;
  }

  try {
    const exportData = {
      query: searchQuery.value,
      timestamp: new Date().toISOString(),
      total: totalResults.value,
      results: searchResults.value.map(result => ({
        content: result.content,
        similarity: result.similarity,
        dataset: getDatasetName(result.kb_id),
        keywords: result.important_keywords
      }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `search_results_${new Date().getTime()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    ElMessage.success('搜索结果已导出');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  }
};

/**
 * 分页变化处理
 */
const handlePageChange = (page: number) => {
  currentPage.value = page;
  searchParams.page = page;
  handleSearch();
};

/**
 * 页面大小变化处理
 */
const handleSizeChange = (size: number) => {
  searchParams.page_size = size;
  currentPage.value = 1;
  searchParams.page = 1;
  handleSearch();
};

/**
 * 前端高亮文本函数 - 确保关键词始终可见
 */
const highlightText = (text: string): string => {
  if (!text || !searchQuery.value.trim()) {
    return text;
  }

  const query = searchQuery.value.trim();
  // 分割查询词，支持中英文
  const keywords = query.split(/[\s\u3000]+/).filter(word => word.length > 0);

  let highlightedText = text;

  // 对每个关键词进行高亮处理
  keywords.forEach(keyword => {
    // 转义特殊字符
    const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`(${escapedKeyword})`, 'gi');
    highlightedText = highlightedText.replace(regex, '<mark class="search-highlight">$1</mark>');
  });

  return highlightedText;
};
</script>

<style scoped>
/* ==========================================================================
   知识库检索页面样式 - 基于Element Plus + vue-next-admin
   ========================================================================== */

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

/* 搜索卡片样式 */
.search-card {
  border: 2px solid var(--el-border-color-light);
  transition: border-color 0.3s ease;
}

.search-card:hover {
  border-color: var(--el-color-primary);
}

.search-container {
  padding: 10px 0;
}

.search-input-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.search-input {
  font-size: 16px;
}

:deep(.search-input .el-textarea__inner) {
  font-size: 16px;
  line-height: 1.6;
  border-radius: 8px;
  border: 2px solid var(--el-border-color-light);
  transition: border-color 0.3s ease;
}

:deep(.search-input .el-textarea__inner:focus) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
}

.search-button {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  min-width: 120px;
}

/* 高级选项样式 */
.advanced-options {
  margin-top: 20px;
  padding: 20px;
  background-color: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
}



/* 历史记录样式 */
.history-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.history-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.history-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 搜索结果样式 */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
}

.results-container {
  min-height: 200px;
}

.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-item {
  padding: 20px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  background-color: var(--el-bg-color);
  cursor: pointer;
  transition: all 0.3s ease;
}

.result-item:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 15px;
}

.result-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex: 1;
}

.result-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.result-content {
  margin: 12px 0;
  line-height: 1.6;
}

.highlighted-content {
  color: var(--el-text-color-primary);
  line-height: 1.6;
  word-break: break-word;
}

.normal-content {
  color: var(--el-text-color-primary);
  line-height: 1.6;
  word-break: break-word;
}

/* 后端返回的高亮样式 - 黄色主题 (支持 mark 和 em 标签) */
:deep(.highlighted-content mark),
:deep(.highlighted-content em) {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
  padding: 3px 6px;
  border-radius: 4px;
  font-weight: 600;
  font-style: normal; /* 重置 em 的斜体样式 */
  border: 1px solid #f1c40f;
  box-shadow: 0 2px 4px rgba(241, 196, 15, 0.3);
  display: inline-block;
  margin: 0 1px;
  transition: all 0.2s ease;
}

:deep(.highlighted-content mark:hover),
:deep(.highlighted-content em:hover) {
  background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(241, 196, 15, 0.4);
}

/* 前端生成的高亮样式 - 蓝色主题 */
:deep(.normal-content mark),
:deep(.normal-content .search-highlight) {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  color: #1890ff;
  padding: 3px 6px;
  border-radius: 4px;
  font-weight: 600;
  border: 1px solid #40a9ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
  display: inline-block;
  margin: 0 1px;
  transition: all 0.2s ease;
}

:deep(.normal-content mark:hover),
:deep(.normal-content .search-highlight:hover) {
  background: linear-gradient(135deg, #bae7ff 0%, #91d5ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(24, 144, 255, 0.4);
}

.normal-content {
  color: var(--el-text-color-primary);
  white-space: pre-wrap;
  word-break: break-word;
}

.result-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.similarity-scores {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

/* 详情对话框样式 */
.result-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.content-detail {
  margin-top: 15px;
  padding: 15px;
  background-color: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
  max-height: 300px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 15px;
  }

  .search-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .search-button {
    width: 100%;
  }

  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .result-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .similarity-scores {
    flex-direction: column;
    gap: 5px;
  }

  .advanced-options {
    padding: 15px;
  }

  :deep(.el-col) {
    margin-bottom: 15px;
  }
}

@media (max-width: 480px) {
  .search-container {
    padding: 5px 0;
  }

  .result-item {
    padding: 15px;
  }

  .result-meta {
    flex-direction: column;
    gap: 6px;
  }

  .history-tags {
    gap: 6px;
  }

  .results-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* 加载动画 */
.search-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  font-size: 16px;
  color: var(--el-text-color-regular);
}

/* 滚动条样式 */
.result-detail::-webkit-scrollbar,
.content-detail::-webkit-scrollbar {
  width: 6px;
}

.result-detail::-webkit-scrollbar-track,
.content-detail::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
  border-radius: 3px;
}

.result-detail::-webkit-scrollbar-thumb,
.content-detail::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

.result-detail::-webkit-scrollbar-thumb:hover,
.content-detail::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}
</style>