#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI聊天API接口

提供AI聊天功能，集成远程AI服务
"""
from typing import List, Optional
import httpx
import json
import time
from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import StreamingResponse
from loguru import logger

from backend.app.iot.schema.ai_chat import (
    ChatRequest,
    ChatResponse,
    ChatMessage,
    ChatStreamResponse
)
from backend.app.iot.service.ai_chat_service import AIChatService
from backend.common.security.jwt import DependsJwtAuth
from backend.core.conf import settings

router = APIRouter()





@router.post("/chat", response_model=ChatResponse, summary="AI聊天对话")
async def chat(
    request_obj: Request,
    chat_request: ChatRequest,
    token: str = DependsJwtAuth
) -> ChatResponse:
    """
    AI聊天对话接口
    
    Args:
        request: 聊天请求参数
        current_user: 当前用户信息
        
    Returns:
        ChatResponse: 聊天响应结果
    """
    try:
        service = AIChatService()
        response = await service.chat(
            message=chat_request.message,
            user_id=request_obj.user.id,  # 从认证中间件设置的用户信息获取
            session_id=chat_request.session_id,
            model=chat_request.model,
            max_tokens=chat_request.max_tokens,
            temperature=chat_request.temperature
        )
        return response
    except Exception as e:
        logger.error(f"AI聊天失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"AI聊天失败: {str(e)}")


@router.post("/chat/stream", summary="AI聊天流式对话")
async def chat_stream(
    request_obj: Request,
    chat_request: ChatRequest,
    token: str = DependsJwtAuth
):
    """
    AI聊天流式对话接口
    
    Args:
        request: 聊天请求参数
        current_user: 当前用户信息
        
    Returns:
        StreamingResponse: 流式响应
    """
    try:
        service = AIChatService()
        
        async def generate_stream():
            async for chunk in service.chat_stream(
                message=chat_request.message,
                user_id=request_obj.user.id,  # 从认证中间件设置的用户信息获取
                session_id=chat_request.session_id,
                model=chat_request.model,
                max_tokens=chat_request.max_tokens,
                temperature=chat_request.temperature
            ):
                yield f"data: {json.dumps(chunk.model_dump(), ensure_ascii=False)}\n\n"
            yield "data: [DONE]\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"
            }
        )
    except Exception as e:
        logger.error(f"AI流式聊天失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"AI流式聊天失败: {str(e)}")


@router.get("/chat/history/{session_id}", response_model=List[ChatMessage], summary="获取聊天历史")
async def get_chat_history(
    request_obj: Request,
    session_id: str,
    token: str = DependsJwtAuth
) -> List[ChatMessage]:
    """
    获取聊天历史记录
    
    Args:
        session_id: 会话ID
        current_user: 当前用户信息
        
    Returns:
        List[ChatMessage]: 聊天历史记录
    """
    try:
        service = AIChatService()
        history = await service.get_chat_history(
            session_id=session_id,
            user_id=request_obj.user.id  # 从认证中间件设置的用户信息获取
        )
        return history
    except Exception as e:
        logger.error(f"获取聊天历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取聊天历史失败: {str(e)}")


@router.delete("/chat/history/{session_id}", summary="清空聊天历史")
async def clear_chat_history(
    request_obj: Request,
    session_id: str,
    token: str = DependsJwtAuth
):
    """
    清空聊天历史记录
    
    Args:
        session_id: 会话ID
        current_user: 当前用户信息
    """
    try:
        service = AIChatService()
        await service.clear_chat_history(
            session_id=session_id,
            user_id=request_obj.user.id  # 从认证中间件设置的用户信息获取
        )
        return {"message": "聊天历史已清空"}
    except Exception as e:
        logger.error(f"清空聊天历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空聊天历史失败: {str(e)}")


@router.get("/models", summary="获取可用AI模型列表")
async def get_available_models(
    request_obj: Request,
    token: str = DependsJwtAuth
):
    """
    获取可用的AI模型列表
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        dict: 可用模型列表
    """
    try:
        service = AIChatService()
        models = await service.get_available_models()
        return {"models": models}
    except Exception as e:
        logger.error(f"获取模型列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")


@router.get("/health", summary="AI服务健康检查")
async def health_check(
    request_obj: Request,
    token: str = DependsJwtAuth
):
    """
    AI服务健康检查
    
    Returns:
        dict: 健康状态信息
    """
    try:
        service = AIChatService()
        health_status = await service.health_check()
        return health_status
    except Exception as e:
        logger.error(f"AI服务健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"AI服务健康检查失败: {str(e)}")
