.markdown-mermaid {
  position: relative;
  width: 100%;
  height: 100%;
  min-width: 100px;
  min-height: 100px;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  // 工具栏容器样式
  .toolbar-container {
    position: relative;
    z-index: 10;
    flex-shrink: 0;
    background: white;

    // 确保自定义头部插槽正确显示
    .custom-mermaid-header {
      position: relative;
      z-index: 11;
    }

    // 默认工具栏的基础样式
    .mermaid-language-tag {
      display: inline-block;
      padding: 8px 12px;
      background: #f0f9ff;
      border: 1px solid #e0f2fe;
      border-radius: 4px;
      font-size: 12px;
      color: #0891b2;
      margin-right: 8px;
    }

    // 简单默认工具栏样式
    .mermaid-default-toolbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      background: white;

      .toolbar-buttons {
        display: flex;
        gap: 4px;

        button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 28px;
          height: 28px;
          border: 1px solid #d1d5db;
          border-radius: 4px;
          background: white;
          color: #374151;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
          }

          &:active {
            background: #e5e7eb;
          }
        }
      }
    }
  }

  .mermaid-content {
    position: relative;
    z-index: 1;
    flex: 1;
    min-height: 200px;
    // max-height: 80vh;
    cursor: grab;

    display: flex;
    align-items: center;
    justify-content: flex-start;
    overflow: hidden; // 防止未缩放的大图表溢出

    &:active {
      cursor: grabbing;
    }

    svg {
      transform-origin: center center; // SVG 的变换原点
      position: relative;
      max-width: 100%;
      max-height: 100%;
    }

    // 渲染状态时的加载效果
    &.rendering {
      svg {
        opacity: 0.8;
        pointer-events: none;
      }
    }
  }

  // 全屏
  &:fullscreen {
    .mermaid-content {
      max-height: 100vh;
      justify-content: center;
    }
  }

  &.dragging {
    .mermaid-content {
      cursor: grabbing;
    }
  }
  &.zoom-limit {
    .mermaid-content {
      transform-origin: center center;
    }
  }

  .mermaid-source-code {
    position: relative;
    z-index: 1;
    flex: 1;
    width: 100%;
    margin: 0;
    padding: 16px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    overflow: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    box-sizing: border-box;
  }
}

// 内容切换过渡 - 减少闪烁，优化为淡入淡出
.content-enter-active {
  transition: opacity 0.2s ease-out;
}

.content-leave-active {
  transition: opacity 0.15s ease-in;
}

.content-enter-from {
  opacity: 0;
}

.content-leave-to {
  opacity: 0;
}

// 工具栏过渡
.toolbar-enter-active,
.toolbar-leave-active {
  transition: all 0.3s ease;
}

.toolbar-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.toolbar-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
