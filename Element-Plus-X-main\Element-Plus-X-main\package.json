{"type": "module", "private": true, "packageManager": "pnpm@10.6.5", "engines": {"node": ">=18"}, "scripts": {"build": "turbo run build", "build:core": "turbo run build --filter=vue-element-plus-x", "publish:core": "cd packages/core && npm publish", "publish:core:beta": "cd packages/core && npm publish --tag beta", "dev:core": "turbo run dev --filter=vue-element-plus-x", "build:storybook": "pnpm run build:storybook --filter=vue-element-plus-x", "dev:metadata": "pnpm run --filter=element-plus-x-metadata dev", "build:metadata": "pnpm run --filter=element-plus-x-metadata build", "dev:docs": "pnpm run dev:metadata && turbo run dev --filter=docs", "build:docs": "pnpm run build:metadata && turbo run build --filter=docs", "ci:publish": "pnpm changeset publish -r --filter=vue-element-plus-x", "dev": "npm run dev:playground", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "lint": "turbo run lint", "lint:fix": "oxlint ./packages --fix && eslint . --fix", "lint:ic": "eslint --inspect-config", "eslint": "eslint .", "eslint:fix": "eslint --fix", "lint-staged": "lint-staged", "prepare": "husky", "cz": "cz"}, "devDependencies": {"@antfu/eslint-config": "^4.16.1", "@changesets/cli": "^2.29.5", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "commitizen": "^4.3.1", "cz-git": "^1.11.2", "eslint": "^9.30.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-oxlint": "^0.16.12", "husky": "^9.1.7", "lint-staged": "^16.1.2", "oxlint": "^0.16.12", "prettier": "^3.6.2", "stylelint": "^16.21.0", "turbo": "^2.5.4", "typescript": "5.8.2"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}