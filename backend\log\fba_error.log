2025-08-27 11:32:22.925 | ERROR    | 52771c18a1b5447e8e38665bf421951d | Exception in ASGI application

  + Exception Group Traceback (most recent call last):
  |
  |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\responses.py", line 271, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                │     │                      └ <anyio._backends._asyncio.TaskGroup object at 0x00000217E9580200>
  |                │     └ <function create_task_group at 0x00000217E126F9C0>
  |                └ <module 'anyio' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\anyio\\__init__.py'>
  |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\anyio\_backends\_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |                    └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x00000217E1479820>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |                  │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x00000217E9...
    |                  │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
    |                  │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |                  │    └ <fastapi.applications.FastAPI object at 0x00000217E6C53800>
    |                  └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x00000217E1479820>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |                            │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x00000217E9...
    |                            │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
    |                            └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |           │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x00000217E9...
    |           │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
    |           │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000217E8F5D3A0>
    |           └ <fastapi.applications.FastAPI object at 0x00000217E6C53800>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    |     raise exc
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |           │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x00000217E93DE700>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x00000217E92A5430>, header_name...
    |           └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000217E8F5D3A0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    |     await self.app(scope, receive, handle_outgoing_request)
    |           │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x00000217E93C9BC0>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x00000217E92A5430>
    |           └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x00000217E92A5430>, header_name...
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    |     raise app_exc
    |           └ AttributeError("'HTTPAuthorizationCredentials' object has no attribute 'get'")
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |           │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000217E9312D40>
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E93C9DA0>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000217E92A4B60>
    |           └ <backend.middleware.access_middleware.AccessMiddleware object at 0x00000217E92A5430>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000217E9312D40>
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E93C9DA0>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x00000217E9080830>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x00000217E92A4B60>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000217E9312D40>
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E93C9DA0>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x00000217E7BD6330>
    |           └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x00000217E9080830>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    |     raise app_exc
    |           └ AttributeError("'HTTPAuthorizationCredentials' object has no attribute 'get'")
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |           │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000217E94EB1A0>
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB240>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x00000217E92522D0>
    |           └ <backend.middleware.state_middleware.StateMiddleware object at 0x00000217E7BD6330>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    |     raise app_exc
    |           └ AttributeError("'HTTPAuthorizationCredentials' object has no attribute 'get'")
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |           │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000217E94EB380>
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000217E9253110>
    |           └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x00000217E92522D0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    |     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
    |           │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000217E94EB380>
    |           │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
    |           │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │                            │    │    └ <starlette.requests.Request object at 0x00000217E93E2F30>
    |           │                            │    └ <fastapi.routing.APIRouter object at 0x00000217E6F1BF50>
    |           │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000217E9253110>
    |           └ <function wrap_app_handling_exceptions at 0x00000217E4231EE0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |           │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000217E94EB560>
    |           │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
    |           │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           └ <fastapi.routing.APIRouter object at 0x00000217E6F1BF50>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 716, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |           │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000217E94EB560>
    |           │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
    |           │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000217E6F1BF50>>
    |           └ <fastapi.routing.APIRouter object at 0x00000217E6F1BF50>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 736, in app
    |     await route.handle(scope, receive, send)
    |           │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000217E94EB560>
    |           │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
    |           │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │     └ <function Route.handle at 0x00000217E4233420>
    |           └ APIRoute(path='/api/iot/v1/ai/chat/stream', name='chat_stream', methods=['POST'])
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 290, in handle
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000217E94EB560>
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <function request_response.<locals>.app at 0x00000217E9296FC0>
    |           └ APIRoute(path='/api/iot/v1/ai/chat/stream', name='chat_stream', methods=['POST'])
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 78, in app
    |     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
    |           │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000217E94EB560>
    |           │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
    |           │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │                            │    └ <starlette.requests.Request object at 0x00000217E9552630>
    |           │                            └ <function request_response.<locals>.app.<locals>.app at 0x00000217E94EB600>
    |           └ <function wrap_app_handling_exceptions at 0x00000217E4231EE0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |           │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000217E95472E0>
    |           │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
    |           │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           └ <function request_response.<locals>.app.<locals>.app at 0x00000217E94EB600>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    |     await response(scope, receive, send)
    |           │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000217E95472E0>
    |           │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
    |           │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           └ <starlette.responses.StreamingResponse object at 0x00000217E9580530>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\responses.py", line 270, in __call__
    |     with collapse_excgroups():
    |          └ <function collapse_excgroups at 0x00000217E41BC860>
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    |     self.gen.throw(value)
    |     │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [AttributeError("'HTTPAuthorizationCredentials' object has no attribute 'ge...
    |     │    │   └ <method 'throw' of 'generator' objects>
    |     │    └ <generator object collapse_excgroups at 0x00000217E95705F0>
    |     └ <contextlib._GeneratorContextManager object at 0x00000217E9580140>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\responses.py", line 274, in wrap
    |     await func()
    |           └ functools.partial(<bound method StreamingResponse.stream_response of <starlette.responses.StreamingResponse object at 0x00000...
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\responses.py", line 254, in stream_response
    |     async for chunk in self.body_iterator:
    |                        │    └ <async_generator object chat_stream.<locals>.generate_stream at 0x00000217E9387340>
    |                        └ <starlette.responses.StreamingResponse object at 0x00000217E9580530>
    |
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\app\iot\api\v1\ai_chat.py", line 81, in generate_stream
    |     user_id=current_user.get("user_id"),
    |             └ HTTPAuthorizationCredentials(scheme='Bearer', credentials='eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjZkMDMzMTE2LTA5MWUtN...
    |
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\pydantic\main.py", line 991, in __getattr__
    |     raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
    |                                  │                                          └ 'get'
    |                                  └ HTTPAuthorizationCredentials(scheme='Bearer', credentials='eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjZkMDMzMTE2LTA5MWUtN...
    |
    | AttributeError: 'HTTPAuthorizationCredentials' object has no attribute 'get'
    +------------------------------------


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 400
               │     └ 3
               └ <function _main at 0x00000217DEFABB00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 400
           │    └ <function BaseProcess._bootstrap at 0x00000217DECABA60>
           └ <SpawnProcess name='SpawnProcess-1' parent=15232 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000217DECAAFC0>
    └ <SpawnProcess name='SpawnProcess-1' parent=15232 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000217DEF69820>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=15232 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=15232 started>
    │    └ <function subprocess_started at 0x00000217E11D3420>
    └ <SpawnProcess name='SpawnProcess-1' parent=15232 started>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=512, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x00000217E13225A0>>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=512, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x00000217E11D2A20>
           │       │   └ <uvicorn.server.Server object at 0x00000217E13225A0>
           │       └ <function run at 0x00000217DE970680>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000217E12FA880>
           │      └ <function Runner.run at 0x00000217E0EDBB00>
           └ <asyncio.runners.Runner object at 0x00000217DEF69640>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000217E0ED96C0>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000217DEF69640>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000217E0ED9620>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000217E0EDB420>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000217E0A0D120>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x00000217E1479820>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x00000217E9...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x00000217E6C53800>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x00000217E1479820>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x00000217E9...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x00000217E9...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000217E8F5D3A0>
          └ <fastapi.applications.FastAPI object at 0x00000217E6C53800>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x00000217E93DE700>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x00000217E92A5430>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000217E8F5D3A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x00000217E93C9BC0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000021...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x00000217E92A5430>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x00000217E92A5430>, header_name...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    raise app_exc
          └ AttributeError("'HTTPAuthorizationCredentials' object has no attribute 'get'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000217E9312D40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E93C9DA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000217E92A4B60>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x00000217E92A5430>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000217E9312D40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E93C9DA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x00000217E9080830>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000217E92A4B60>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000217E9312D40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E93C9DA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x00000217E7BD6330>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x00000217E9080830>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    raise app_exc
          └ AttributeError("'HTTPAuthorizationCredentials' object has no attribute 'get'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000217E94EB1A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB240>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x00000217E92522D0>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x00000217E7BD6330>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    raise app_exc
          └ AttributeError("'HTTPAuthorizationCredentials' object has no attribute 'get'")
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000217E94EB380>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000217E9253110>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x00000217E92522D0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000217E94EB380>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000217E93E2F30>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000217E6F1BF50>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000217E9253110>
          └ <function wrap_app_handling_exceptions at 0x00000217E4231EE0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000217E94EB560>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000217E6F1BF50>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000217E94EB560>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000217E6F1BF50>>
          └ <fastapi.routing.APIRouter object at 0x00000217E6F1BF50>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000217E94EB560>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000217E4233420>
          └ APIRoute(path='/api/iot/v1/ai/chat/stream', name='chat_stream', methods=['POST'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000217E94EB560>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000217E9296FC0>
          └ APIRoute(path='/api/iot/v1/ai/chat/stream', name='chat_stream', methods=['POST'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000217E94EB560>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000217E9552630>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x00000217E94EB600>
          └ <function wrap_app_handling_exceptions at 0x00000217E4231EE0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000217E95472E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x00000217E94EB600>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await response(scope, receive, send)
          │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000217E95472E0>
          │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000217E94EB060>
          │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <starlette.responses.StreamingResponse object at 0x00000217E9580530>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\responses.py", line 270, in __call__
    with collapse_excgroups():
         └ <function collapse_excgroups at 0x00000217E41BC860>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [AttributeError("'HTTPAuthorizationCredentials' object has no attribute 'ge...
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x00000217E95705F0>
    └ <contextlib._GeneratorContextManager object at 0x00000217E9580140>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\responses.py", line 274, in wrap
    await func()
          └ functools.partial(<bound method StreamingResponse.stream_response of <starlette.responses.StreamingResponse object at 0x00000...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\responses.py", line 254, in stream_response
    async for chunk in self.body_iterator:
                       │    └ <async_generator object chat_stream.<locals>.generate_stream at 0x00000217E9387340>
                       └ <starlette.responses.StreamingResponse object at 0x00000217E9580530>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\app\iot\api\v1\ai_chat.py", line 81, in generate_stream
    user_id=current_user.get("user_id"),
            └ HTTPAuthorizationCredentials(scheme='Bearer', credentials='eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjZkMDMzMTE2LTA5MWUtN...

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\pydantic\main.py", line 991, in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
                                 │                                          └ 'get'
                                 └ HTTPAuthorizationCredentials(scheme='Bearer', credentials='eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjZkMDMzMTE2LTA5MWUtN...

AttributeError: 'HTTPAuthorizationCredentials' object has no attribute 'get'
2025-08-27 11:41:15.733 | ERROR    | f22d6368deef4815876e37955fb2d5ab | Exception in ASGI application

  + Exception Group Traceback (most recent call last):
  |
  |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\responses.py", line 271, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                │     │                      └ <anyio._backends._asyncio.TaskGroup object at 0x0000020442C44C20>
  |                │     └ <function create_task_group at 0x000002043A95F9C0>
  |                └ <module 'anyio' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\anyio\\__init__.py'>
  |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\anyio\_backends\_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |                    └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002043ABB97C0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |                  │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020442...
    |                  │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
    |                  │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |                  │    └ <fastapi.applications.FastAPI object at 0x000002044067B3E0>
    |                  └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002043ABB97C0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |                            │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020442...
    |                            │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
    |                            └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |           │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020442...
    |           │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
    |           │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000204428654C0>
    |           └ <fastapi.applications.FastAPI object at 0x000002044067B3E0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    |     raise exc
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |           │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000020442A93CE0>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x00000204411682F0>, header_name...
    |           └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000204428654C0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    |     await self.app(scope, receive, handle_outgoing_request)
    |           │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000020442A93100>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x00000204411682F0>
    |           └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x00000204411682F0>, header_name...
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    |     raise app_exc
    |           └ TypeError('Object of type datetime is not JSON serializable')
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |           │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020442AF8040>
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442A93EC0>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000204411684D0>
    |           └ <backend.middleware.access_middleware.AccessMiddleware object at 0x00000204411682F0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020442AF8040>
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442A93EC0>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x00000204411683E0>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x00000204411684D0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020442AF8040>
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442A93EC0>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x0000020441168410>
    |           └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x00000204411683E0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    |     raise app_exc
    |           └ TypeError('Object of type datetime is not JSON serializable')
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |           │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020442B49D00>
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442A92E80>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x0000020441168350>
    |           └ <backend.middleware.state_middleware.StateMiddleware object at 0x0000020441168410>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    |     raise app_exc
    |           └ TypeError('Object of type datetime is not JSON serializable')
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |           │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020442C6B100>
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000020441168140>
    |           └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x0000020441168350>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    |     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
    |           │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020442C6B100>
    |           │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
    |           │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │                            │    │    └ <starlette.requests.Request object at 0x0000020442AB2450>
    |           │                            │    └ <fastapi.routing.APIRouter object at 0x00000204406C4560>
    |           │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000020441168140>
    |           └ <function wrap_app_handling_exceptions at 0x000002043D961EE0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |           │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020442C6B420>
    |           │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
    |           │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           └ <fastapi.routing.APIRouter object at 0x00000204406C4560>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 716, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |           │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020442C6B420>
    |           │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
    |           │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000204406C4560>>
    |           └ <fastapi.routing.APIRouter object at 0x00000204406C4560>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 736, in app
    |     await route.handle(scope, receive, send)
    |           │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020442C6B420>
    |           │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
    |           │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │     └ <function Route.handle at 0x000002043D963420>
    |           └ APIRoute(path='/api/iot/v1/ai/chat/stream', name='chat_stream', methods=['POST'])
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 290, in handle
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020442C6B420>
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <function request_response.<locals>.app at 0x0000020442A16FC0>
    |           └ APIRoute(path='/api/iot/v1/ai/chat/stream', name='chat_stream', methods=['POST'])
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 78, in app
    |     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
    |           │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020442C6B420>
    |           │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
    |           │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │                            │    └ <starlette.requests.Request object at 0x0000020442B66C90>
    |           │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000020442C6B4C0>
    |           └ <function wrap_app_handling_exceptions at 0x000002043D961EE0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |           │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020442C6B600>
    |           │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
    |           │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           └ <function request_response.<locals>.app.<locals>.app at 0x0000020442C6B4C0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    |     await response(scope, receive, send)
    |           │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020442C6B600>
    |           │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
    |           │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           └ <starlette.responses.StreamingResponse object at 0x0000020442C450A0>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\responses.py", line 270, in __call__
    |     with collapse_excgroups():
    |          └ <function collapse_excgroups at 0x000002043D8EC860>
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    |     self.gen.throw(value)
    |     │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError('Object of type datetime is not JSON serializable')])
    |     │    │   └ <method 'throw' of 'generator' objects>
    |     │    └ <generator object collapse_excgroups at 0x0000020442B8F920>
    |     └ <contextlib._GeneratorContextManager object at 0x0000020442C44A70>
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\responses.py", line 274, in wrap
    |     await func()
    |           └ functools.partial(<bound method StreamingResponse.stream_response of <starlette.responses.StreamingResponse object at 0x00000...
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\responses.py", line 254, in stream_response
    |     async for chunk in self.body_iterator:
    |                        │    └ <async_generator object chat_stream.<locals>.generate_stream at 0x0000020442C6C440>
    |                        └ <starlette.responses.StreamingResponse object at 0x0000020442C450A0>
    |
    |   File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\app\iot\api\v1\ai_chat.py", line 92, in generate_stream
    |     yield f"data: {json.dumps(chunk.model_dump(), ensure_ascii=False)}\n\n"
    |                    │    │     │     └ <function BaseModel.model_dump at 0x000002043D6EFA60>
    |                    │    │     └ ChatStreamResponse(id='24a529bf-fe57-4e7e-966b-8154d4070422', delta='<think>', session_id='debug_session_123', model_used='Qw...
    |                    │    └ <function dumps at 0x000002043A63F920>
    |                    └ <module 'json' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py'>
    |
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\__init__.py", line 238, in dumps
    |     **kw).encode(obj)
    |       │          └ {'id': '24a529bf-fe57-4e7e-966b-8154d4070422', 'delta': '<think>', 'session_id': 'debug_session_123', 'model_used': 'Qwen3-32...
    |       └ {}
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\encoder.py", line 200, in encode
    |     chunks = self.iterencode(o, _one_shot=True)
    |              │    │          └ {'id': '24a529bf-fe57-4e7e-966b-8154d4070422', 'delta': '<think>', 'session_id': 'debug_session_123', 'model_used': 'Qwen3-32...
    |              │    └ <function JSONEncoder.iterencode at 0x000002043A63FCE0>
    |              └ <json.encoder.JSONEncoder object at 0x0000020442A7AAE0>
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\encoder.py", line 258, in iterencode
    |     return _iterencode(o, 0)
    |            │           └ {'id': '24a529bf-fe57-4e7e-966b-8154d4070422', 'delta': '<think>', 'session_id': 'debug_session_123', 'model_used': 'Qwen3-32...
    |            └ <_json.Encoder object at 0x0000020442CB23E0>
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\encoder.py", line 180, in default
    |     raise TypeError(f'Object of type {o.__class__.__name__} '
    |                                       │ │         └ <member '__name__' of 'getset_descriptor' objects>
    |                                       │ └ <attribute '__class__' of 'object' objects>
    |                                       └ datetime.datetime(2025, 8, 27, 11, 41, 15, 731326)
    |
    | TypeError: Object of type datetime is not JSON serializable
    +------------------------------------


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 420
               │     └ 3
               └ <function _main at 0x00000204386BBB00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 420
           │    └ <function BaseProcess._bootstrap at 0x00000204383BBA60>
           └ <SpawnProcess name='SpawnProcess-1' parent=38624 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000204383BAFC0>
    └ <SpawnProcess name='SpawnProcess-1' parent=38624 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000020438679820>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=38624 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=38624 started>
    │    └ <function subprocess_started at 0x000002043A8A3420>
    └ <SpawnProcess name='SpawnProcess-1' parent=38624 started>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=764, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002043AA12390>>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=764, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002043A8A2A20>
           │       │   └ <uvicorn.server.Server object at 0x000002043AA12390>
           │       └ <function run at 0x0000020438060680>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002043A9EA880>
           │      └ <function Runner.run at 0x000002043A18BB00>
           └ <asyncio.runners.Runner object at 0x000002043A831E50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002043A1896C0>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002043A831E50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002043A189620>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002043A18B420>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002043A0BD120>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002043ABB97C0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020442...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002044067B3E0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002043ABB97C0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020442...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020442...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000204428654C0>
          └ <fastapi.applications.FastAPI object at 0x000002044067B3E0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000020442A93CE0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x00000204411682F0>, header_name...
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000204428654C0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\asgi_correlation_id\middleware.py", line 90, in __call__
    await self.app(scope, receive, handle_outgoing_request)
          │    │   │      │        └ <function CorrelationIdMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000020442A93100>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.access_middleware.AccessMiddleware object at 0x00000204411682F0>
          └ CorrelationIdMiddleware(app=<backend.middleware.access_middleware.AccessMiddleware object at 0x00000204411682F0>, header_name...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    raise app_exc
          └ TypeError('Object of type datetime is not JSON serializable')
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020442AF8040>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442A93EC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000204411684D0>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x00000204411682F0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020442AF8040>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442A93EC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x00000204411683E0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000204411684D0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 48, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020442AF8040>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442A93EC0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.state_middleware.StateMiddleware object at 0x0000020441168410>
          └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x00000204411683E0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    raise app_exc
          └ TypeError('Object of type datetime is not JSON serializable')
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020442B49D00>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442A92E80>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x0000020441168350>
          └ <backend.middleware.state_middleware.StateMiddleware object at 0x0000020441168410>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 189, in __call__
    raise app_exc
          └ TypeError('Object of type datetime is not JSON serializable')
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020442C6B100>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000020441168140>
          └ <backend.middleware.opera_log_middleware.OperaLogMiddleware object at 0x0000020441168350>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000020442C6B100>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000020442AB2450>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000204406C4560>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000020441168140>
          └ <function wrap_app_handling_exceptions at 0x000002043D961EE0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020442C6B420>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000204406C4560>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020442C6B420>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000204406C4560>>
          └ <fastapi.routing.APIRouter object at 0x00000204406C4560>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020442C6B420>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000002043D963420>
          └ APIRoute(path='/api/iot/v1/ai/chat/stream', name='chat_stream', methods=['POST'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020442C6B420>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000020442A16FC0>
          └ APIRoute(path='/api/iot/v1/ai/chat/stream', name='chat_stream', methods=['POST'])
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020442C6B420>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000020442B66C90>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000020442C6B4C0>
          └ <function wrap_app_handling_exceptions at 0x000002043D961EE0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020442C6B600>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000020442C6B4C0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await response(scope, receive, send)
          │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020442C6B600>
          │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000020442C6B060>
          │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <starlette.responses.StreamingResponse object at 0x0000020442C450A0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\responses.py", line 270, in __call__
    with collapse_excgroups():
         └ <function collapse_excgroups at 0x000002043D8EC860>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [TypeError('Object of type datetime is not JSON serializable')])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x0000020442B8F920>
    └ <contextlib._GeneratorContextManager object at 0x0000020442C44A70>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\_utils.py", line 83, in collapse_excgroups
    raise exc
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\responses.py", line 274, in wrap
    await func()
          └ functools.partial(<bound method StreamingResponse.stream_response of <starlette.responses.StreamingResponse object at 0x00000...
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\responses.py", line 254, in stream_response
    async for chunk in self.body_iterator:
                       │    └ <async_generator object chat_stream.<locals>.generate_stream at 0x0000020442C6C440>
                       └ <starlette.responses.StreamingResponse object at 0x0000020442C450A0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\app\iot\api\v1\ai_chat.py", line 92, in generate_stream
    yield f"data: {json.dumps(chunk.model_dump(), ensure_ascii=False)}\n\n"
                   │    │     │     └ <function BaseModel.model_dump at 0x000002043D6EFA60>
                   │    │     └ ChatStreamResponse(id='24a529bf-fe57-4e7e-966b-8154d4070422', delta='<think>', session_id='debug_session_123', model_used='Qw...
                   │    └ <function dumps at 0x000002043A63F920>
                   └ <module 'json' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py'>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
      │          └ {'id': '24a529bf-fe57-4e7e-966b-8154d4070422', 'delta': '<think>', 'session_id': 'debug_session_123', 'model_used': 'Qwen3-32...
      └ {}
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             │    │          └ {'id': '24a529bf-fe57-4e7e-966b-8154d4070422', 'delta': '<think>', 'session_id': 'debug_session_123', 'model_used': 'Qwen3-32...
             │    └ <function JSONEncoder.iterencode at 0x000002043A63FCE0>
             └ <json.encoder.JSONEncoder object at 0x0000020442A7AAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           │           └ {'id': '24a529bf-fe57-4e7e-966b-8154d4070422', 'delta': '<think>', 'session_id': 'debug_session_123', 'model_used': 'Qwen3-32...
           └ <_json.Encoder object at 0x0000020442CB23E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                                      │ │         └ <member '__name__' of 'getset_descriptor' objects>
                                      │ └ <attribute '__class__' of 'object' objects>
                                      └ datetime.datetime(2025, 8, 27, 11, 41, 15, 731326)

TypeError: Object of type datetime is not JSON serializable
2025-08-27 11:43:24.801 | ERROR    | 579ba1dd4b5a4c899196c9a6a06879e5 | AI服务调用失败: 400, 响应: b'{"object":"error","message":"This model\'s maximum context length is 40960 tokens. However, you requested 40994 tokens (34 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.","type":"BadRequestError","param":null,"code":400}'
2025-08-27 11:43:24.802 | ERROR    | 579ba1dd4b5a4c899196c9a6a06879e5 | AI流式聊天失败: AI服务调用失败: 400, 详情: b'{"object":"error","message":"This model\'s maximum context length is 40960 tokens. However, you requested 40994 tokens (34 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.","type":"BadRequestError","param":null,"code":400}'
2025-08-27 11:43:24.802 | ERROR    | 579ba1dd4b5a4c899196c9a6a06879e5 | 请求参数: {'model': 'Qwen3-32B-AWQ', 'messages': [{'role': 'system', 'content': '你是一个专业的AI助手，能够提供准确、有用的回答。请用中文回复用户的问题。'}, {'role': 'user', 'content': '你好'}], 'max_tokens': 40960, 'temperature': 0.7, 'top_p': 0.9, 'stream': True}
2025-08-27 11:43:24.803 | ERROR    | 579ba1dd4b5a4c899196c9a6a06879e5 | 会话ID: chat_session_1756266200313_yvh98bm44zd, 用户ID: 1
