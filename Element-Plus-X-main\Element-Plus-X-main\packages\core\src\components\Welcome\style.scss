.welcome-container {
  --border-radius: 8px;
  --icon-size: 64px;
  --icon-size-small: 48px;
  --gap: 16px;
  --gap-small: 8px;
  --padding: 24px;
  --color-filled-bg: #e6f4ff;
  --color-filled-border: #91caff;
  --color-title: rgba(0, 0, 0, 0.88);
  --color-description: rgba(0, 0, 0, 0.65);

  display: flex;
  gap: var(--gap);
  padding: var(--padding);
  border-radius: var(--border-radius);

  &.welcome-filled {
    background-color: var(--color-filled-bg);
    border: 1px solid var(--color-filled-border);
  }

  &.welcome-borderless {
    border: none;
  }

  &.welcome-rtl {
    direction: rtl;
  }
}

.welcome-icon {
  // 图标容器样式
  flex: 0 0 auto;
  width: var(--icon-size);
  height: var(--icon-size);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: calc(var(--border-radius) / 2);
  overflow: hidden;
  font-size: 24px;

  .icon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    padding: 4px;
  }

  // 小尺寸适配
  @media (max-width: 480px) {
    width: var(--icon-size-small);
    height: var(--icon-size-small);
  }
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--gap-small);
}

.title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--gap-small);
}

.welcome-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-title);
}

.welcome-extra {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: var(--gap-small);

  /* 如果内容需要换行 */
  :deep(*) {
    flex-shrink: 0;
  }
}

.welcome-description {
  margin: 0;
  font-size: 14px;
  color: var(--color-description);
  line-height: 1.5;
}
