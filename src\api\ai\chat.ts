/**
 * AI聊天相关API接口
 */
import { fastApiRequest } from '@/config/knowledgeBase';
import { Session } from '@/utils/storage';

// ==================== 数据类型定义 ====================

/**
 * 聊天消息
 */
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  session_id?: string;
  user_id?: number;
}

/**
 * 聊天请求参数
 */
export interface ChatRequest {
  message: string;
  session_id?: string;
  model?: string;
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  stream?: boolean;
}

/**
 * 聊天响应
 */
export interface ChatResponse {
  id: string;
  reply: string;
  session_id: string;
  model_used: string;
  tokens_used: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  response_time: number;
  timestamp: string;
}

/**
 * 流式聊天响应
 */
export interface ChatStreamResponse {
  id: string;
  delta: string;
  session_id: string;
  model_used: string;
  finish_reason?: string;
  timestamp: string;
}

/**
 * AI模型信息
 */
export interface AIModelInfo {
  name: string;
  display_name: string;
  description: string;
  max_tokens: number;
  supports_stream: boolean;
  status: string;
}

/**
 * 健康状态
 */
export interface HealthStatus {
  status: string;
  ai_service_url: string;
  ai_service_status: string;
  response_time: number;
  available_models: string[];
  timestamp: string;
}

// ==================== 工具函数 ====================

/**
 * 获取认证token
 * @returns 格式化的Authorization头
 */
function getAuthorizationHeader(): string {
  const token = Session.get('token');
  if (!token) {
    return '';
  }
  // 如果token已经包含Bearer前缀，直接使用；否则添加Bearer前缀
  return token.startsWith('Bearer ') ? token : `Bearer ${token}`;
}

// ==================== API接口函数 ====================

/**
 * AI聊天对话
 * @param params 聊天请求参数
 * @returns 聊天响应
 */
export async function chatWithAI(params: ChatRequest): Promise<ChatResponse> {
  try {
    const authHeader = getAuthorizationHeader();
    if (!authHeader) {
      throw new Error('用户未登录，请先登录后再试');
    }

    const response = await fastApiRequest.post('/api/iot/v1/ai/chat', params);
    return response.data;
  } catch (error: any) {
    console.error('AI聊天失败:', error);
    if (error.response?.status === 401) {
      throw new Error('认证失败，请重新登录');
    }
    throw new Error(error.response?.data?.detail || 'AI聊天失败');
  }
}

/**
 * AI流式聊天对话
 * @param params 聊天请求参数
 * @param onMessage 消息回调函数
 * @param onError 错误回调函数
 * @param onComplete 完成回调函数
 */
export async function chatWithAIStream(
  params: ChatRequest,
  onMessage: (response: ChatStreamResponse) => void,
  onError: (error: Error) => void,
  onComplete: () => void
): Promise<void> {
  try {
    const authHeader = getAuthorizationHeader();
    if (!authHeader) {
      throw new Error('用户未登录，请先登录后再试');
    }

    const response = await fetch(`${fastApiRequest.defaults.baseURL}/api/iot/v1/ai/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              onComplete();
              return;
            }

            try {
              const streamResponse: ChatStreamResponse = JSON.parse(data);
              onMessage(streamResponse);
            } catch (parseError) {
              console.warn('解析流式响应失败:', parseError);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    onComplete();
  } catch (error: any) {
    console.error('AI流式聊天失败:', error);
    onError(new Error(error.message || 'AI流式聊天失败'));
  }
}

/**
 * 获取聊天历史记录
 * @param sessionId 会话ID
 * @returns 聊天历史记录
 */
export async function getChatHistory(sessionId: string): Promise<ChatMessage[]> {
  try {
    const authHeader = getAuthorizationHeader();
    if (!authHeader) {
      throw new Error('用户未登录，请先登录后再试');
    }

    const response = await fastApiRequest.get(`/api/iot/v1/ai/chat/history/${sessionId}`);
    return response.data;
  } catch (error: any) {
    console.error('获取聊天历史失败:', error);
    if (error.response?.status === 401) {
      throw new Error('认证失败，请重新登录');
    }
    throw new Error(error.response?.data?.detail || '获取聊天历史失败');
  }
}

/**
 * 清空聊天历史记录
 * @param sessionId 会话ID
 */
export async function clearChatHistory(sessionId: string): Promise<void> {
  try {
    const authHeader = getAuthorizationHeader();
    if (!authHeader) {
      throw new Error('用户未登录，请先登录后再试');
    }

    await fastApiRequest.delete(`/api/iot/v1/ai/chat/history/${sessionId}`);
  } catch (error: any) {
    console.error('清空聊天历史失败:', error);
    if (error.response?.status === 401) {
      throw new Error('认证失败，请重新登录');
    }
    throw new Error(error.response?.data?.detail || '清空聊天历史失败');
  }
}

/**
 * 获取可用AI模型列表
 * @returns 可用模型列表
 */
export async function getAvailableModels(): Promise<AIModelInfo[]> {
  try {
    const authHeader = getAuthorizationHeader();
    if (!authHeader) {
      throw new Error('用户未登录，请先登录后再试');
    }

    const response = await fastApiRequest.get('/api/iot/v1/ai/models');
    return response.data.models;
  } catch (error: any) {
    console.error('获取模型列表失败:', error);
    if (error.response?.status === 401) {
      throw new Error('认证失败，请重新登录');
    }
    throw new Error(error.response?.data?.detail || '获取模型列表失败');
  }
}

/**
 * AI服务健康检查
 * @returns 健康状态信息
 */
export async function checkAIHealth(): Promise<HealthStatus> {
  try {
    const authHeader = getAuthorizationHeader();
    if (!authHeader) {
      throw new Error('用户未登录，请先登录后再试');
    }

    const response = await fastApiRequest.get('/api/iot/v1/ai/health');
    return response.data;
  } catch (error: any) {
    console.error('AI服务健康检查失败:', error);
    if (error.response?.status === 401) {
      throw new Error('认证失败，请重新登录');
    }
    throw new Error(error.response?.data?.detail || 'AI服务健康检查失败');
  }
}

// ==================== 工具函数 ====================

/**
 * 生成会话ID
 * @returns 新的会话ID
 */
export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
}

/**
 * 格式化token使用情况
 * @param tokens token使用统计
 * @returns 格式化的字符串
 */
export function formatTokenUsage(tokens: ChatResponse['tokens_used']): string {
  return `输入: ${tokens.prompt_tokens} | 输出: ${tokens.completion_tokens} | 总计: ${tokens.total_tokens}`;
}

/**
 * 计算消息长度（估算token数量）
 * @param message 消息内容
 * @returns 估算的token数量
 */
export function estimateTokenCount(message: string): number {
  // 简单估算：中文字符按1.5个token计算，英文单词按1个token计算
  const chineseChars = (message.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishWords = (message.match(/[a-zA-Z]+/g) || []).length;
  const otherChars = message.length - chineseChars - englishWords;
  
  return Math.ceil(chineseChars * 1.5 + englishWords + otherChars * 0.5);
}
