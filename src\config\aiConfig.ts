/**
 * AI服务配置
 */

// AI服务配置接口
export interface AIConfig {
  serviceUrl: string;
  defaultModel: string;
  maxTokens: number;
  defaultTemperature: number;
  defaultTopP: number;
  streamTimeout: number;
  healthCheckInterval: number;
}

// 默认AI配置
const defaultConfig: AIConfig = {
  serviceUrl: 'http://192.168.2.188:8888/v1',
  defaultModel: 'Qwen3-32B-AWQ',
  maxTokens: 40960,
  defaultTemperature: 0.7,
  defaultTopP: 0.9,
  streamTimeout: 120000, // 2分钟
  healthCheckInterval: 300000, // 5分钟
};

// 从环境变量获取配置
export const aiConfig: AIConfig = {
  serviceUrl: import.meta.env.VITE_AI_SERVICE_URL || defaultConfig.serviceUrl,
  defaultModel: import.meta.env.VITE_AI_DEFAULT_MODEL || defaultConfig.defaultModel,
  maxTokens: parseInt(import.meta.env.VITE_AI_MAX_TOKENS) || defaultConfig.maxTokens,
  defaultTemperature: defaultConfig.defaultTemperature,
  defaultTopP: defaultConfig.defaultTopP,
  streamTimeout: defaultConfig.streamTimeout,
  healthCheckInterval: defaultConfig.healthCheckInterval,
};

// 聊天配置
export const chatConfig = {
  // 会话配置
  sessionIdPrefix: 'chat_session_',
  maxHistoryMessages: 20,
  
  // UI配置
  messageMaxLength: 4000,
  autoScrollDelay: 100,
  typingIndicatorDelay: 500,
  
  // 重试配置
  maxRetries: 3,
  retryDelay: 1000,
  
  // 导出配置
  exportFilename: 'AI聊天记录',
  exportDateFormat: 'YYYY-MM-DD HH:mm:ss',
};

// 模型配置
export const modelConfig = {
  'Qwen3-32B-AWQ': {
    displayName: '通义千问3-32B',
    description: '阿里巴巴通义千问3代32B参数模型，支持中英文对话',
    maxTokens: 40960,
    supportsStream: true,
    icon: '🤖',
  },
  'Qwen2.5-72B': {
    displayName: '通义千问2.5-72B',
    description: '阿里巴巴通义千问2.5代72B参数模型，更强的推理能力',
    maxTokens: 32768,
    supportsStream: true,
    icon: '🧠',
  },
};

// 预设提示词
export const promptTemplates = [
  {
    name: '通用助手',
    prompt: '你是一个专业的AI助手，能够提供准确、有用的回答。请用中文回复用户的问题。',
    category: 'general',
  },
  {
    name: '技术专家',
    prompt: '你是一个技术专家，擅长解答编程、软件开发、系统架构等技术问题。请提供详细、准确的技术解答。',
    category: 'technical',
  },
  {
    name: '学习导师',
    prompt: '你是一个耐心的学习导师，善于解释复杂概念，提供学习建议和指导。请用通俗易懂的方式回答问题。',
    category: 'education',
  },
  {
    name: '创意助手',
    prompt: '你是一个富有创意的助手，能够提供创新的想法、解决方案和建议。请发挥想象力，提供有创意的回答。',
    category: 'creative',
  },
];

// 快捷消息模板
export const quickMessages = [
  '你好，请介绍一下你的功能和能力。',
  '我在学习Vue.js，有什么好的建议吗？',
  '如何提高前端开发效率？',
  '请帮我分析一下现代Web开发的趋势。',
  '能否推荐一些学习人工智能的资源？',
  '如何设计一个高可用的系统架构？',
];

// 错误消息配置
export const errorMessages = {
  networkError: '网络连接失败，请检查网络设置',
  serviceUnavailable: 'AI服务暂时不可用，请稍后重试',
  authenticationFailed: '身份验证失败，请重新登录',
  rateLimitExceeded: '请求过于频繁，请稍后再试',
  invalidInput: '输入内容无效，请检查后重试',
  tokenLimitExceeded: '消息长度超出限制，请缩短内容',
  sessionExpired: '会话已过期，请刷新页面',
  unknownError: '发生未知错误，请联系技术支持',
};

// 成功消息配置
export const successMessages = {
  messageSent: '消息发送成功',
  historyCleaned: '聊天记录已清空',
  historyExported: '聊天记录已导出',
  settingsSaved: '设置已保存',
  connectionRestored: '连接已恢复',
};

// 工具函数
export const aiUtils = {
  /**
   * 生成会话ID
   */
  generateSessionId(): string {
    return `${chatConfig.sessionIdPrefix}${Date.now()}_${Math.random().toString(36).substring(2)}`;
  },

  /**
   * 格式化token使用情况
   */
  formatTokenUsage(tokens: { prompt_tokens: number; completion_tokens: number; total_tokens: number }): string {
    return `输入: ${tokens.prompt_tokens} | 输出: ${tokens.completion_tokens} | 总计: ${tokens.total_tokens}`;
  },

  /**
   * 估算消息token数量
   */
  estimateTokenCount(message: string): number {
    // 简单估算：中文字符按1.5个token计算，英文单词按1个token计算
    const chineseChars = (message.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (message.match(/[a-zA-Z]+/g) || []).length;
    const otherChars = message.length - chineseChars - englishWords;
    
    return Math.ceil(chineseChars * 1.5 + englishWords + otherChars * 0.5);
  },

  /**
   * 检查消息是否超出长度限制
   */
  isMessageTooLong(message: string): boolean {
    return message.length > chatConfig.messageMaxLength;
  },

  /**
   * 获取模型显示信息
   */
  getModelInfo(modelName: string) {
    return modelConfig[modelName as keyof typeof modelConfig] || {
      displayName: modelName,
      description: '未知模型',
      maxTokens: aiConfig.maxTokens,
      supportsStream: true,
      icon: '❓',
    };
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp: number | string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (date.toDateString() === now.toDateString()) { // 今天
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  },
};

export default aiConfig;
