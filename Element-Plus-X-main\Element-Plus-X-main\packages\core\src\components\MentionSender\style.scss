.el-sender {
  width: 100%;
  display: flex;
  flex-direction: column;

  position: relative;
  box-sizing: border-box;
  box-shadow: var(--el-box-shadow-tertiary);
  transition: background var(--el-transition-duration);
  border-radius: calc(var(--el-border-radius-base) * 2);
  border-color: var(--el-border-color);
  border-width: 0;
  border-style: solid;
  transition: width var(--el-sender-header-animation-duration);

  &:after {
    content: '';
    position: absolute;
    inset: 0;
    pointer-events: none;
    transition: border-color var(--el-transition-duration);
    border-radius: inherit;
    border-style: inherit;
    border-color: inherit;
    border-width: var(--el-border-width);
  }
  &:focus-within {
    box-shadow: var(--el-box-shadow);
    border-color: var(--el-color-primary);
    &::after {
      border-width: 2px;
    }
  }

  .el-sender-header-wrap {
    display: flex;
    flex-direction: column;
    gap: var(--el-padding-xs, 8px);
    width: 100%;
    margin: 0;
    padding: 0;
  }

  // 展开收起动画
  // calc-size 新特性，解决无法对某些非固定尺寸（如 auto、min-content、max-content 等）进行动画过渡的新特性
  .slide-enter-active,
  .slide-leave-active {
    height: calc-size(max-content, size);
    opacity: 1;
    transition:
      height var(--el-sender-header-animation-duration),
      opacity var(--el-sender-header-animation-duration),
      border var(--el-sender-header-animation-duration);
    overflow: hidden;
  }

  .slide-enter-from,
  .slide-leave-to {
    height: 0;
    opacity: 0;
  }

  .el-sender-header {
    border-bottom-width: var(--el-border-width);
    border-bottom-style: solid;
    border-bottom-color: var(--el-border-color);
  }

  .el-sender-content {
    display: flex;
    gap: var(--el-padding-xs, 8px);
    width: 100%;
    padding-block: var(--el-padding-sm, 12px);
    padding-inline-start: var(--el-padding, 16px);
    padding-inline-end: var(--el-padding-sm, 12px);
    box-sizing: border-box;
    align-items: flex-end;
    // 前缀
    .el-sender-prefix {
      flex: none;
    }
    // 输入框
    .el-sender-input {
      height: 100%;
      display: flex;
      align-items: center;
      align-self: center;
    }
    // el-mention
    .el-mention {
      height: 100%;
      display: flex;
      align-items: center;
      align-self: center;
    }
    :deep(.el-textarea__inner) {
      padding: 0;
      margin: 0;
      color: var(--el-text-color-primary);
      font-size: var(--el-sender-input-input-font-size);
      line-height: var(--el-font-line-height-primary);
      list-style: none;
      position: relative;
      display: inline-block;
      box-sizing: border-box;
      width: 100%;
      min-width: 0;
      max-width: 100%;
      height: auto;
      min-height: auto !important;
      border-radius: 0;
      border: none;
      flex: auto;
      align-self: center;
      vertical-align: bottom;
      resize: none;
      background-color: transparent;
      transition:
        all var(--el-transition-duration),
        height 0s;
      box-shadow: none !important;
    }
    // 操作列表
    .el-sender-action-list-presets {
      display: flex;
      gap: var(--el-padding-xs, 8px);
      flex-direction: row-reverse;
    }
  }

  // 变体样式 --variant
  .content-variant-updown {
    display: flex;
    flex-direction: column;
    align-items: initial;
    .el-sender-updown-wrap {
      display: flex;
      justify-content: space-between;
      gap: 8px;
      // 前缀
      .el-sender-prefix {
        flex: initial;
      }

      .el-sender-action-list {
        margin-left: auto;
      }
    }
  }

  // 底部容器
  .el-sender-footer {
    border-top-width: var(--el-border-width);
    border-top-style: solid;
    border-top-color: var(--el-border-color);
  }
}

.el-sender-disabled {
  background-color: var(--el-fill-color);
  pointer-events: none;
}
