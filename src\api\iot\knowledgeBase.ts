import axios from 'axios';
import { Session } from '/@/utils/storage';
import { ElMessage } from 'element-plus';

// 知识库相关接口

/**
 * 知识库数据类型定义
 */
export interface KnowledgeBase {
  id?: string;
  name: string;
  avatar?: string;
  description?: string;
  embedding_model?: string;
  permission?: 'me' | 'team';
  chunk_method?: string;
  pagerank?: number;
  parser_config?: any;
  chunk_count?: number;
  document_count?: number;
  token_num?: number;
  status?: string;
  language?: string;
  similarity_threshold?: number;
  vector_similarity_weight?: number;
  create_time?: number;
  update_time?: number;
  create_date?: string;
  update_date?: string;
  created_by?: string;
  tenant_id?: string;
}

// 类型别名，保持向后兼容
export type KnowledgeBaseInfo = KnowledgeBase;

/**
 * 创建知识库请求参数 - 符合RAGFlow API规范
 */
export interface CreateKnowledgeBaseParams {
  name: string;
  avatar?: string;
  description?: string;
  embedding_model?: string;
  permission?: 'me' | 'team';
  chunk_method?: 'naive' | 'manual' | 'qa' | 'table' | 'paper' | 'book' | 'laws' | 'presentation' | 'picture' | 'one' | 'knowledge_graph' | 'email';
  pagerank?: number;
  parser_config?: Record<string, any>;
}

/**
 * 更新知识库请求参数 - 符合RAGFlow API规范
 */
export interface UpdateKnowledgeBaseParams {
  name?: string;
  description?: string;
  pagerank?: number;
  // 注意：embedding_model 和 chunk_method 字段已移除
  // 这些字段在编辑时不允许修改，使用后端默认值
}

/**
 * 知识库列表查询参数
 */
export interface KnowledgeBaseQueryParams {
  page?: number;
  page_size?: number;
  orderby?: 'create_time' | 'update_time';
  desc?: boolean;
  name?: string;
  id?: string;
}

/**
 * 知识库统计信息
 */
export interface KnowledgeBaseStats {
  total_kb: number;
  total_documents: number;
  total_chunks: number;
  total_tokens: number;
  active_kb: number;
  recent_created: number;
  storage_used: string;
  last_update: string;
}

/**
 * API 响应格式
 */
export interface ApiResponse<T = any> {
  code: number;
  message?: string;
  data?: T;
}

// 创建专门用于FastAPI的axios实例
export const fastApiRequest = axios.create({
  baseURL: (() => {
    // 优先使用环境变量配置的FastAPI URL
    const fastApiUrl = import.meta.env.VITE_FASTAPI_URL;
    if (fastApiUrl) {
      return fastApiUrl;
    }

    // 开发环境使用代理
    if (import.meta.env.DEV) {
      return '/fastapi';
    }

    // 生产环境直接访问FastAPI服务
    return 'http://localhost:8000';
  })(),
  timeout: 50000,
  headers: { 'Content-Type': 'application/json' },
});

// 添加请求拦截器 - 自动添加token
fastApiRequest.interceptors.request.use(
  (config) => {
    if (Session.get('token')) {
      const token = Session.get('token');
      // 如果 token 已经包含 Bearer 前缀，直接使用；否则添加 Bearer 前缀
      config.headers!['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 添加响应拦截器 - 处理错误
fastApiRequest.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      ElMessage.error('认证失败，请重新登录');
    } else if (error.response?.status === 403) {
      ElMessage.error('权限不足');
    } else if (error.response?.status === 404) {
      ElMessage.error('接口不存在');
    } else {
      ElMessage.error(error.message || '请求失败');
    }
    return Promise.reject(error);
  }
);

// API基础URL通过fastApiRequest实例自动处理，无需手动管理

/**
 * 创建知识库 - 使用符合RAGFlow规范的接口
 */
export function createKnowledgeBase(data: CreateKnowledgeBaseParams) {
  // 如果没有指定嵌入模型，不传递该字段，让RAGFlow使用默认模型
  const requestData = { ...data };

  // 只有明确指定了embedding_model才传递，否则让系统使用默认模型
  // 注意：如果要指定模型，必须使用 <model_name>@<provider> 格式
  if (!data.embedding_model) {
    delete requestData.embedding_model;
  }

  return fastApiRequest({
    url: `/api/iot/v1/knowledge-base`,
    method: 'post',
    data: requestData
  });
}

/**
 * 获取知识库列表
 */
export function getKnowledgeBaseList(params?: KnowledgeBaseQueryParams) {
  return fastApiRequest({
    url: `/api/iot/v1/knowledge-base/list`,
    method: 'get',
    params
  });
}

/**
 * 获取知识库详情
 */
export function getKnowledgeBaseDetail(id: string) {
  return fastApiRequest({
    url: `/api/iot/v1/knowledge-base/${id}`,
    method: 'get'
  });
}

/**
 * 更新知识库
 */
export function updateKnowledgeBase(id: string, data: UpdateKnowledgeBaseParams) {
  return fastApiRequest({
    url: `/api/iot/v1/knowledge-base/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除知识库
 */
export function deleteKnowledgeBases(ids?: string[]) {
  return fastApiRequest({
    url: `/api/iot/v1/knowledge-base/delete`,
    method: 'delete',
    data: { ids }
  });
}

/**
 * 获取知识库统计信息
 */
export function getKnowledgeBaseStats() {
  return fastApiRequest({
    url: `/api/iot/v1/knowledge-base/stats/overview`,
    method: 'get'
  });
}

/**
 * 知识库服务健康检查
 */
export function checkKnowledgeBaseHealth() {
  return fastApiRequest({
    url: `/api/iot/v1/knowledge-base/health`,
    method: 'get'
  });
}

/**
 * 批量删除知识库
 */
export function batchDeleteKnowledgeBases(ids: string[]) {
  return deleteKnowledgeBases(ids);
}

/**
 * 删除所有知识库
 */
export function deleteAllKnowledgeBases() {
  return deleteKnowledgeBases(undefined);
}

/**
 * 搜索知识库
 */
export function searchKnowledgeBases(keyword: string, params?: Omit<KnowledgeBaseQueryParams, 'name'>) {
  return getKnowledgeBaseList({
    ...params,
    name: keyword
  });
}

/**
 * 获取知识库分页列表
 */
export function getKnowledgeBasePageList(page: number = 1, pageSize: number = 30, params?: Omit<KnowledgeBaseQueryParams, 'page' | 'page_size'>) {
  return getKnowledgeBaseList({
    page,
    page_size: pageSize,
    ...params
  });
}

/**
 * 复制知识库（基于现有知识库创建新的）
 */
export async function copyKnowledgeBase(sourceId: string, newName: string) {
  try {
    const response = await getKnowledgeBaseDetail(sourceId);
    if (response.data && response.data.code === 200) {
      const sourceKb = response.data.data;
      const newKbData: CreateKnowledgeBaseParams = {
        name: newName,
        description: sourceKb.description ? `${sourceKb.description} (副本)` : '知识库副本',
        embedding_model: sourceKb.embedding_model || 'bge-m3:latest@Ollama',
        permission: sourceKb.permission || 'me',
        chunk_method: sourceKb.chunk_method || 'naive',
        pagerank: sourceKb.pagerank || 0,
        parser_config: sourceKb.parser_config
      };
      return createKnowledgeBase(newKbData);
    }
    throw new Error('获取源知识库信息失败');
  } catch (error) {
    throw new Error('复制知识库失败');
  }
}

/**
 * 检查知识库名称是否可用
 */
export async function checkKnowledgeBaseName(name: string) {
  try {
    const response = await getKnowledgeBaseList({ name });
    if (response.data && response.data.code === 200) {
      return Array.isArray(response.data.data) ? response.data.data.length === 0 : true;
    }
    return false;
  } catch (error) {
    return false;
  }
}

// 嵌入模型已固化为 bge-m3:latest@Ollama，不再需要动态获取
// 相关函数已移除以符合架构简化方案

/**
 * 获取分块方法列表
 */
export function getChunkMethods() {
  return Promise.resolve([
    { label: '通用', value: 'naive' },
    { label: '书籍', value: 'book' },
    { label: '邮件', value: 'email' },
    { label: '法律', value: 'laws' },
    { label: '手动', value: 'manual' },
    { label: '单一', value: 'one' },
    { label: '论文', value: 'paper' },
    { label: '图片', value: 'picture' },
    { label: '演示文稿', value: 'presentation' },
    { label: '问答', value: 'qa' },
    { label: '表格', value: 'table' },
    { label: '标签', value: 'tag' }
  ]);
}

// ==================== 知识库检索相关接口 ====================

/**
 * 知识库检索请求参数
 */
export interface KnowledgeRetrievalParams {
  question: string;
  dataset_ids?: string[];
  document_ids?: string[];
  page?: number;
  page_size?: number;
  similarity_threshold?: number;
  vector_similarity_weight?: number;
  top_k?: number;
  rerank_id?: string;
  keyword?: boolean;
  highlight?: boolean;
}

/**
 * 检索结果中的单个块信息
 */
export interface SearchResult {
  id: string;
  content: string;
  content_ltks?: string;
  document_id: string;
  document_keyword?: string;
  highlight?: string;
  image_id?: string;
  important_keywords: string[];
  kb_id: string;
  positions: string[];
  similarity: number;
  term_similarity: number;
  vector_similarity: number;
}

/**
 * 知识库检索响应数据
 */
export interface KnowledgeRetrievalResponse {
  chunks: SearchResult[];
  doc_aggs: any[];
  total: number;
}

/**
 * 知识库检索
 * @param params 检索参数
 * @returns 检索结果
 */
export async function searchKnowledge(params: KnowledgeRetrievalParams) {
  try {
    const response = await fastApiRequest.post('/api/iot/v1/knowledge-base/retrieval', params);
    return response;
  } catch (error: any) {
    console.error('知识库检索失败:', error);

    if (error.response?.status === 401) {
      ElMessage.error('认证失败，请重新登录');
      // 可以在这里触发登录跳转
    } else if (error.response?.status === 403) {
      ElMessage.error('没有检索权限');
    } else if (error.response?.status === 400) {
      ElMessage.error(error.response.data?.message || '请求参数错误');
    } else {
      ElMessage.error('检索失败，请稍后重试');
    }

    throw error;
  }
}

// ==================== AI总结相关接口 ====================

/**
 * AI总结请求参数
 */
export interface AISummaryParams {
  query: string;           // 用户原始查询
  contents: string[];      // 检索到的内容片段
  max_tokens?: number;     // 最大token数量
  temperature?: number;    // 生成温度
  model?: string;         // 使用的模型名称
}

/**
 * AI总结响应数据
 */
export interface AISummaryResponse {
  summary: string;         // 生成的总结
  token_usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  model: string;          // 使用的模型
  finish_reason: string;  // 完成原因
}

/**
 * vLLM API兼容的消息格式
 */
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

/**
 * vLLM API请求参数
 */
export interface VLLMChatParams {
  model: string;
  messages: ChatMessage[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  stream?: boolean;
}

/**
 * vLLM API响应格式
 */
export interface VLLMChatResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: ChatMessage;
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * 调用vLLM服务生成AI总结
 * @param params 总结参数
 * @returns 总结结果
 */
export async function generateAISummary(params: AISummaryParams): Promise<AISummaryResponse> {
  try {
    // 构建系统提示词
    const systemPrompt = `你是一个专业的知识总结助手。请根据用户的查询和提供的相关内容片段，生成一个准确、全面、结构化的总结。

要求：
1. 总结应该直接回答用户的查询问题
2. 整合多个内容片段中的相关信息
3. 保持逻辑清晰，结构合理
4. 突出重点信息，去除冗余内容
5. 使用中文回答，语言简洁明了
6. 如果内容片段中没有相关信息，请明确说明

请基于以下内容生成总结：`;

    // 构建用户消息
    const userContent = `用户查询：${params.query}

相关内容片段：
${params.contents.map((content, index) => `${index + 1}. ${content}`).join('\n\n')}

请基于以上内容生成总结：`;

    // 构建vLLM API请求
    const vllmParams: VLLMChatParams = {
      model: params.model || 'Qwen3-32B-AWQ',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userContent }
      ],
      max_tokens: params.max_tokens || 2048,
      temperature: params.temperature || 0.7,
      top_p: 0.9,
      stream: false
    };

    // 调用vLLM API
    const response = await fetch('http://192.168.2.188:8888/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(vllmParams)
    });

    if (!response.ok) {
      throw new Error(`vLLM API调用失败: ${response.status} ${response.statusText}`);
    }

    const vllmResponse: VLLMChatResponse = await response.json();

    // 转换为标准格式
    return {
      summary: vllmResponse.choices[0]?.message?.content || '生成总结失败',
      token_usage: vllmResponse.usage,
      model: vllmResponse.model,
      finish_reason: vllmResponse.choices[0]?.finish_reason || 'unknown'
    };

  } catch (error: any) {
    console.error('AI总结生成失败:', error);

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      ElMessage.error('无法连接到AI服务，请检查网络连接');
    } else if (error.message.includes('vLLM API')) {
      ElMessage.error('AI服务调用失败，请稍后重试');
    } else {
      ElMessage.error('生成总结失败，请稍后重试');
    }

    throw error;
  }
}

/**
 * 检查vLLM服务状态
 */
export async function checkVLLMStatus(): Promise<boolean> {
  try {
    const response = await fetch('http://192.168.2.188:8888/v1/models', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    return response.ok;
  } catch (error) {
    console.error('vLLM服务检查失败:', error);
    return false;
  }
}

/**
 * 获取检索历史
 * @param page 页码
 * @param pageSize 每页大小
 * @returns 检索历史
 */
export async function getRetrievalHistory(page: number = 1, pageSize: number = 20) {
  try {
    const response = await fastApiRequest.get('/api/iot/v1/knowledge-base/retrieval/history', {
      params: { page, page_size: pageSize }
    });

    return response;
  } catch (error: any) {
    console.error('获取检索历史失败:', error);

    if (error.response?.status === 401) {
      ElMessage.error('认证失败，请重新登录');
    } else if (error.response?.status === 403) {
      ElMessage.error('没有查看历史权限');
    } else {
      ElMessage.error('获取检索历史失败');
    }

    throw error;
  }
}
