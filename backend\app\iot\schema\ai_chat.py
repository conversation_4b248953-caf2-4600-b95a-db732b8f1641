#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI聊天相关的数据模型

定义AI聊天请求和响应的数据结构
"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class ChatMessage(BaseModel):
    """聊天消息模型"""
    id: str = Field(..., description="消息ID")
    role: str = Field(..., description="角色：user/assistant/system")
    content: str = Field(..., description="消息内容")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    session_id: Optional[str] = Field(None, description="会话ID")
    user_id: Optional[int] = Field(None, description="用户ID")


class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., min_length=1, max_length=4000, description="用户消息")
    session_id: Optional[str] = Field(None, description="会话ID，用于保持对话上下文")
    model: Optional[str] = Field("Qwen3-32B-AWQ", description="使用的AI模型")
    max_tokens: Optional[int] = Field(4096, ge=1, le=40960, description="最大token数量")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="生成温度")
    top_p: Optional[float] = Field(0.9, ge=0.0, le=1.0, description="Top-p采样参数")
    stream: Optional[bool] = Field(False, description="是否使用流式响应")


class ChatResponse(BaseModel):
    """聊天响应模型"""
    id: str = Field(..., description="响应ID")
    reply: str = Field(..., description="AI回复内容")
    session_id: str = Field(..., description="会话ID")
    model_used: str = Field(..., description="使用的模型")
    tokens_used: Dict[str, int] = Field(..., description="token使用情况")
    response_time: float = Field(..., description="响应时间(秒)")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")


class ChatStreamResponse(BaseModel):
    """流式聊天响应模型"""
    id: str = Field(..., description="响应ID")
    delta: str = Field(..., description="增量内容")
    session_id: str = Field(..., description="会话ID")
    model_used: str = Field(..., description="使用的模型")
    finish_reason: Optional[str] = Field(None, description="完成原因")


class VLLMChatRequest(BaseModel):
    """vLLM API请求模型"""
    model: str = Field(..., description="模型名称")
    messages: List[Dict[str, str]] = Field(..., description="消息列表")
    max_tokens: Optional[int] = Field(4096, description="最大token数量")
    temperature: Optional[float] = Field(0.7, description="生成温度")
    top_p: Optional[float] = Field(0.9, description="Top-p采样参数")
    stream: Optional[bool] = Field(False, description="是否流式响应")


class VLLMChatResponse(BaseModel):
    """vLLM API响应模型"""
    id: str = Field(..., description="响应ID")
    object: str = Field(..., description="对象类型")
    created: int = Field(..., description="创建时间戳")
    model: str = Field(..., description="模型名称")
    choices: List[Dict[str, Any]] = Field(..., description="选择列表")
    usage: Dict[str, int] = Field(..., description="使用统计")


class AIModelInfo(BaseModel):
    """AI模型信息"""
    name: str = Field(..., description="模型名称")
    display_name: str = Field(..., description="显示名称")
    description: str = Field(..., description="模型描述")
    max_tokens: int = Field(..., description="最大token数量")
    supports_stream: bool = Field(True, description="是否支持流式响应")
    status: str = Field("available", description="模型状态")


class ChatSession(BaseModel):
    """聊天会话模型"""
    session_id: str = Field(..., description="会话ID")
    user_id: int = Field(..., description="用户ID")
    title: Optional[str] = Field(None, description="会话标题")
    model: str = Field(..., description="使用的模型")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    message_count: int = Field(0, description="消息数量")


class HealthStatus(BaseModel):
    """健康状态模型"""
    status: str = Field(..., description="服务状态")
    ai_service_url: str = Field(..., description="AI服务地址")
    ai_service_status: str = Field(..., description="AI服务状态")
    response_time: float = Field(..., description="响应时间")
    available_models: List[str] = Field(..., description="可用模型列表")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")


class ChatError(BaseModel):
    """聊天错误模型"""
    error_code: str = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")
