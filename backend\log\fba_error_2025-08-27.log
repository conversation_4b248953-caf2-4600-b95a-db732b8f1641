2025-08-26 08:44:46.942 | ERROR    | c19c6038302345618a7f4530ac58ca15 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:44:46.961 | ERROR    | 59dd918c9b43443eb84e95caf4c7c53f | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:44:46.962 | ERROR    | e9267cdc7e024e03ab3bc866e6e2e14d | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:45:23.794 | ERROR    | c64b9855de8241118e407d7fcc581ad7 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:45:23.798 | ERROR    | 97148c06f8f2467aa76bdf8056238e18 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:45:23.804 | ERROR    | 7ec79687580d49c7b897c5c4ae9c5777 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:45:34.746 | ERROR    | ced5ea6a06694884b79c988f06c796d5 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:45:39.331 | ERROR    | 72594b73a35344d5ab3d123e0d8fb868 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:45:55.765 | ERROR    | 05fcfbb9759343b99b7f8c39a6e7fef1 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:45:55.768 | ERROR    | 139f90869dab44b38991641a47afc57f | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:45:55.773 | ERROR    | 072863778f374f26a1e2ea9a2f4b285e | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:46:51.823 | ERROR    | fea032939abd4acb98ffae3f794b92ad | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:46:52.779 | ERROR    | 203c1c3c1eea4766a59ea5e47589e16a | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:46:54.556 | ERROR    | dfebb5f262f84e41ae867e6ff27ca2a7 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:46:54.770 | ERROR    | 82a4bbc6224c4b8d953f1f8ad4977816 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:46:54.773 | ERROR    | f57463fc281b4e49b05b47c03b3aeb24 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:46:54.776 | ERROR    | 83a765fdf22846f6b8946e3a31e8e746 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:46:55.441 | ERROR    | d753829cb57b4b99b3663a3368dec8ac | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:47:00.139 | ERROR    | 241659b7b64545f48f73c7c1cc9ec472 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:47:00.143 | ERROR    | 0edcc0cc5d9d4760b6dbb9fba5781fbb | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:47:00.150 | ERROR    | 2ff5d952ba7d487d99af3d9c05c7f9f3 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:47:01.361 | ERROR    | 0a9a40068a8c4c6dad05000f788a7713 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:47:45.012 | ERROR    | 71f2685e1d6f41c3ab454eb28c5ae327 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:47:45.012 | ERROR    | 3789c7ca19e4460f93e11e9fcd33fbbd | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:47:45.015 | ERROR    | 925c124673bf4707885899a488131e3f | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:48:25.390 | ERROR    | 5d050feb1d144546a60924a5c0f3d05b | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:48:53.918 | ERROR    | 35f682fc56604d3480846135e7d27ad7 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:48:53.938 | ERROR    | a9a10028c3c8453390805931d7c1d795 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:48:53.947 | ERROR    | dab519f00dbd421799ff4d7f478fce06 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:48:55.683 | ERROR    | 9a674ac04847441392654225c9811235 | Java token认证失败: 401: Token 无效或已过期
2025-08-26 08:54:15.290 | ERROR    | - | ❌ 数据库 redis 连接认证失败
2025-08-26 09:16:06.224 | ERROR    | c7c685aed7fb4706959720e12d2b7ddc | JWT 授权异常：cannot access local variable 'json' where it is not associated with a value
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 159, in decode
    payload = jws.verify(token, key, algorithms, verify=verify_signature)
              │   │      │      │    │                  └ True
              │   │      │      │    └ ['HS256']
              │   │      │      └ 'abcdefghijklfastbeesmartrstuvwxyz'
              │   │      └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9.9IkfgE6HxYuU67r8dK_dG3oHcR...
              │   └ <function verify at 0x0000028CE7C9FC40>
              └ <module 'jose.jws' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jws.py'>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 77, in verify
    _verify_signature(signing_input, header, signature, key, algorithms)
    │                 │              │       │          │    └ ['HS256']
    │                 │              │       │          └ 'abcdefghijklfastbeesmartrstuvwxyz'
    │                 │              │       └ b"\xf4\x89\x1f\x80N\x87\xc5\x8b\x94\xeb\xba\xfct\xaf\xdd\x1bz\x07q\x18'\x88\x05nO/Q\x8e\xeeKN\xea\xde\x0b[\x1c&\x1a\x01\x92\x...
    │                 │              └ {'alg': 'HS512'}
    │                 └ b'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9'
    └ <function _verify_signature at 0x0000028CE7D87740>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 259, in _verify_signature
    raise JWSError("The specified alg value is not allowed")
          └ <class 'jose.exceptions.JWSError'>

jose.exceptions.JWSError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 96, in jwt_decode
    payload = jwt.decode(
              │   └ <function decode at 0x0000028CE7C9FA60>
              └ <module 'jose.jwt' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jwt.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 161, in decode
    raise JWTError(e)
          └ <class 'jose.exceptions.JWTError'>

jose.exceptions.JWTError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 286, in jwt_authentication
    token_payload = jwt_decode(token)
                    │          └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9.9IkfgE6HxYuU67r8dK_dG3oHcR...
                    └ <function jwt_decode at 0x0000028CE7DFDC60>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 110, in jwt_decode
    raise errors.TokenError(msg='Token 无效')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 89, in authenticate_java_token
    raise errors.TokenError(msg='Token 无效或已过期')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效或已过期


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x0000028CE0EEA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000028CE3428B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000028CE342BA60>
    └ <uvicorn.server.Server object at 0x0000028CE9E30BF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000028CE342BB00>
           │       │   └ <uvicorn.server.Server object at 0x0000028CE9E30BF0>
           │       └ <function run at 0x0000028CE2C0F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000028CEB4B0E40>
           │      └ <function Runner.run at 0x0000028CE2CAB2E0>
           └ <asyncio.runners.Runner object at 0x0000028CE384F8F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000028CE2CA8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000028CE384F8F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000028CE2D78D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000028CE2CAAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000028CE2C04860>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000028CEB881580>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028CEB881B20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000028CEB55A750>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000028CEB55A780>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000028CEB881580>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028CEB881B20>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000028CE6501260>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000028CEB55A750>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000028CEB55A750...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028CEB881B20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000028CEB55A7B0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000028CEB55A750>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 36, in __call__
    auth_result = await self.backend.authenticate(conn)
                        │    │       │            └ <starlette.requests.HTTPConnection object at 0x0000028CEB892A80>
                        │    │       └ <function JwtAuthMiddleware.authenticate at 0x0000028CE74B37E0>
                        │    └ <backend.middleware.jwt_auth_middleware.JwtAuthMiddleware object at 0x0000028CE91B2270>
                        └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000028CEB55A7B0>

> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 74, in authenticate
    user = await jwt_authentication(token)
                 │                  └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9.9IkfgE6HxYuU67r8dK_dG3oHcR...
                 └ <function jwt_authentication at 0x0000028CE7DFE160>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 315, in jwt_authentication
    return await java_adapter.authenticate_java_token(token)
                 │            │                       └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9.9IkfgE6HxYuU67r8dK_dG3oHcR...
                 │            └ <function JavaAdapter.authenticate_java_token at 0x0000028CE7DFD940>
                 └ <backend.common.security.java_adapter.JavaAdapter object at 0x0000028CE8DDDB20>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 108, in authenticate_java_token
    except json.JSONDecodeError as e:
           │    └ <class 'json.decoder.JSONDecodeError'>
           └ <module 'json' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py'>

UnboundLocalError: cannot access local variable 'json' where it is not associated with a value
2025-08-26 09:16:06.248 | ERROR    | 97c29822aea040d4bd238437e96d91eb | JWT 授权异常：cannot access local variable 'json' where it is not associated with a value
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 159, in decode
    payload = jws.verify(token, key, algorithms, verify=verify_signature)
              │   │      │      │    │                  └ True
              │   │      │      │    └ ['HS256']
              │   │      │      └ 'abcdefghijklfastbeesmartrstuvwxyz'
              │   │      └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9.9IkfgE6HxYuU67r8dK_dG3oHcR...
              │   └ <function verify at 0x0000028CE7C9FC40>
              └ <module 'jose.jws' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jws.py'>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 77, in verify
    _verify_signature(signing_input, header, signature, key, algorithms)
    │                 │              │       │          │    └ ['HS256']
    │                 │              │       │          └ 'abcdefghijklfastbeesmartrstuvwxyz'
    │                 │              │       └ b"\xf4\x89\x1f\x80N\x87\xc5\x8b\x94\xeb\xba\xfct\xaf\xdd\x1bz\x07q\x18'\x88\x05nO/Q\x8e\xeeKN\xea\xde\x0b[\x1c&\x1a\x01\x92\x...
    │                 │              └ {'alg': 'HS512'}
    │                 └ b'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9'
    └ <function _verify_signature at 0x0000028CE7D87740>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 259, in _verify_signature
    raise JWSError("The specified alg value is not allowed")
          └ <class 'jose.exceptions.JWSError'>

jose.exceptions.JWSError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 96, in jwt_decode
    payload = jwt.decode(
              │   └ <function decode at 0x0000028CE7C9FA60>
              └ <module 'jose.jwt' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jwt.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 161, in decode
    raise JWTError(e)
          └ <class 'jose.exceptions.JWTError'>

jose.exceptions.JWTError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 286, in jwt_authentication
    token_payload = jwt_decode(token)
                    │          └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9.9IkfgE6HxYuU67r8dK_dG3oHcR...
                    └ <function jwt_decode at 0x0000028CE7DFDC60>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 110, in jwt_decode
    raise errors.TokenError(msg='Token 无效')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 89, in authenticate_java_token
    raise errors.TokenError(msg='Token 无效或已过期')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效或已过期


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x0000028CE0EEA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000028CE3428B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000028CE342BA60>
    └ <uvicorn.server.Server object at 0x0000028CE9E30BF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000028CE342BB00>
           │       │   └ <uvicorn.server.Server object at 0x0000028CE9E30BF0>
           │       └ <function run at 0x0000028CE2C0F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000028CEB4B0E40>
           │      └ <function Runner.run at 0x0000028CE2CAB2E0>
           └ <asyncio.runners.Runner object at 0x0000028CE384F8F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000028CE2CA8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000028CE384F8F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000028CE2D78D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000028CE2CAAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000028CE2C04860>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000028CEB880F40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028CEB8814E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000028CEB55A750>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000028CEB55A780>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000028CEB880F40>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028CEB8814E0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000028CE6501260>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000028CEB55A750>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000028CEB55A750...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028CEB8814E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000028CEB55A7B0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000028CEB55A750>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 36, in __call__
    auth_result = await self.backend.authenticate(conn)
                        │    │       │            └ <starlette.requests.HTTPConnection object at 0x0000028CEB8914C0>
                        │    │       └ <function JwtAuthMiddleware.authenticate at 0x0000028CE74B37E0>
                        │    └ <backend.middleware.jwt_auth_middleware.JwtAuthMiddleware object at 0x0000028CE91B2270>
                        └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000028CEB55A7B0>

> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 74, in authenticate
    user = await jwt_authentication(token)
                 │                  └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9.9IkfgE6HxYuU67r8dK_dG3oHcR...
                 └ <function jwt_authentication at 0x0000028CE7DFE160>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 315, in jwt_authentication
    return await java_adapter.authenticate_java_token(token)
                 │            │                       └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9.9IkfgE6HxYuU67r8dK_dG3oHcR...
                 │            └ <function JavaAdapter.authenticate_java_token at 0x0000028CE7DFD940>
                 └ <backend.common.security.java_adapter.JavaAdapter object at 0x0000028CE8DDDB20>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 108, in authenticate_java_token
    except json.JSONDecodeError as e:
           │    └ <class 'json.decoder.JSONDecodeError'>
           └ <module 'json' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py'>

UnboundLocalError: cannot access local variable 'json' where it is not associated with a value
2025-08-26 09:16:06.259 | ERROR    | de0811816e7547caa55e3129327dcc9a | JWT 授权异常：cannot access local variable 'json' where it is not associated with a value
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 159, in decode
    payload = jws.verify(token, key, algorithms, verify=verify_signature)
              │   │      │      │    │                  └ True
              │   │      │      │    └ ['HS256']
              │   │      │      └ 'abcdefghijklfastbeesmartrstuvwxyz'
              │   │      └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9.9IkfgE6HxYuU67r8dK_dG3oHcR...
              │   └ <function verify at 0x0000028CE7C9FC40>
              └ <module 'jose.jws' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jws.py'>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 77, in verify
    _verify_signature(signing_input, header, signature, key, algorithms)
    │                 │              │       │          │    └ ['HS256']
    │                 │              │       │          └ 'abcdefghijklfastbeesmartrstuvwxyz'
    │                 │              │       └ b"\xf4\x89\x1f\x80N\x87\xc5\x8b\x94\xeb\xba\xfct\xaf\xdd\x1bz\x07q\x18'\x88\x05nO/Q\x8e\xeeKN\xea\xde\x0b[\x1c&\x1a\x01\x92\x...
    │                 │              └ {'alg': 'HS512'}
    │                 └ b'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9'
    └ <function _verify_signature at 0x0000028CE7D87740>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 259, in _verify_signature
    raise JWSError("The specified alg value is not allowed")
          └ <class 'jose.exceptions.JWSError'>

jose.exceptions.JWSError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 96, in jwt_decode
    payload = jwt.decode(
              │   └ <function decode at 0x0000028CE7C9FA60>
              └ <module 'jose.jwt' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jwt.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 161, in decode
    raise JWTError(e)
          └ <class 'jose.exceptions.JWTError'>

jose.exceptions.JWTError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 286, in jwt_authentication
    token_payload = jwt_decode(token)
                    │          └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9.9IkfgE6HxYuU67r8dK_dG3oHcR...
                    └ <function jwt_decode at 0x0000028CE7DFDC60>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 110, in jwt_decode
    raise errors.TokenError(msg='Token 无效')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 89, in authenticate_java_token
    raise errors.TokenError(msg='Token 无效或已过期')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效或已过期


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x0000028CE0EEA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000028CE3428B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000028CE342BA60>
    └ <uvicorn.server.Server object at 0x0000028CE9E30BF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000028CE342BB00>
           │       │   └ <uvicorn.server.Server object at 0x0000028CE9E30BF0>
           │       └ <function run at 0x0000028CE2C0F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000028CEB4B0E40>
           │      └ <function Runner.run at 0x0000028CE2CAB2E0>
           └ <asyncio.runners.Runner object at 0x0000028CE384F8F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000028CE2CA8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000028CE384F8F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000028CE2D78D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000028CE2CAAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000028CE2C04860>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000028CEB8807C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028CEB881C60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000028CEB55A750>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000028CEB55A780>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000028CEB8807C0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028CEB881C60>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000028CE6501260>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000028CEB55A750>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000028CEB55A750...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000028CEB881C60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000028CEB55A7B0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000028CEB55A750>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 36, in __call__
    auth_result = await self.backend.authenticate(conn)
                        │    │       │            └ <starlette.requests.HTTPConnection object at 0x0000028CEB892390>
                        │    │       └ <function JwtAuthMiddleware.authenticate at 0x0000028CE74B37E0>
                        │    └ <backend.middleware.jwt_auth_middleware.JwtAuthMiddleware object at 0x0000028CE91B2270>
                        └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000028CEB55A7B0>

> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 74, in authenticate
    user = await jwt_authentication(token)
                 │                  └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9.9IkfgE6HxYuU67r8dK_dG3oHcR...
                 └ <function jwt_authentication at 0x0000028CE7DFE160>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 315, in jwt_authentication
    return await java_adapter.authenticate_java_token(token)
                 │            │                       └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3NWM4ODI3LTFlOTUtNDAyOS1hOWU2LWJmZmJkYjcwNTI3MyJ9.9IkfgE6HxYuU67r8dK_dG3oHcR...
                 │            └ <function JavaAdapter.authenticate_java_token at 0x0000028CE7DFD940>
                 └ <backend.common.security.java_adapter.JavaAdapter object at 0x0000028CE8DDDB20>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 108, in authenticate_java_token
    except json.JSONDecodeError as e:
           │    └ <class 'json.decoder.JSONDecodeError'>
           └ <module 'json' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py'>

UnboundLocalError: cannot access local variable 'json' where it is not associated with a value
2025-08-26 09:22:35.831 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x0000028CE0EEA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000028CE3428B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000028CE342BA60>
    └ <uvicorn.server.Server object at 0x0000028CE9E30BF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000028CE342BB00>
           │       │   └ <uvicorn.server.Server object at 0x0000028CE9E30BF0>
           │       └ <function run at 0x0000028CE2C0F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000028CEB4B0E40>
           │      └ <function Runner.run at 0x0000028CE2CAB2E0>
           └ <asyncio.runners.Runner object at 0x0000028CE384F8F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000028CE2CA8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000028CE384F8F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000028CE2D78D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000028CE2CAAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000028CE2C04860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1976, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 64610)>
    └ <_ProactorSocketTransport closing fd=1976>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-26 14:25:50.626 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x0000028CE0EEA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000028CE3428B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000028CE342BA60>
    └ <uvicorn.server.Server object at 0x0000028CE9E30BF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000028CE342BB00>
           │       │   └ <uvicorn.server.Server object at 0x0000028CE9E30BF0>
           │       └ <function run at 0x0000028CE2C0F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000028CEB4B0E40>
           │      └ <function Runner.run at 0x0000028CE2CAB2E0>
           └ <asyncio.runners.Runner object at 0x0000028CE384F8F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000028CE2CA8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000028CE384F8F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000028CE2D78D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000028CE2CAAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000028CE2C04860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1140, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 51553)>
    └ <_ProactorSocketTransport closing fd=1140>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-26 14:56:29.899 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x0000028CE0EEA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000028CE3428B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000028CE342BA60>
    └ <uvicorn.server.Server object at 0x0000028CE9E30BF0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000028CE342BB00>
           │       │   └ <uvicorn.server.Server object at 0x0000028CE9E30BF0>
           │       └ <function run at 0x0000028CE2C0F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000028CEB4B0E40>
           │      └ <function Runner.run at 0x0000028CE2CAB2E0>
           └ <asyncio.runners.Runner object at 0x0000028CE384F8F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000028CE2CA8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000028CE384F8F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000028CE2D78D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000028CE2CAAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000028CE2C04860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=232, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 52638)>
    └ <_ProactorSocketTransport closing fd=232>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
