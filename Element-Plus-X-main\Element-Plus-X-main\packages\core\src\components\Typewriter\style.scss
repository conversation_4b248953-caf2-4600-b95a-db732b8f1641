.typer-container {
  overflow-x: auto;
}
/* Markdown基础样式 */
.markdown-content :deep(ul) {
  list-style-type: disc;
}

// 新增 md 雾化效果
// 添加对 h1-h6, ol, ul 的特殊处理
.typing-markdown-cursor-foggy,
.typing-cursor-foggy {
  &.markdown-content :deep() h1,
  &.markdown-content :deep() h2,
  &.markdown-content :deep() h3,
  &.markdown-content :deep() h4,
  &.markdown-content :deep() h5,
  &.markdown-content :deep() h6,
  &.markdown-content :deep() p,
  &.markdown-content :deep() ol:last-child li,
  &.markdown-content :deep() ul:last-child li {
    position: relative;
    overflow: hidden;

    &:last-child:after {
      content: '';
      width: var(--cursor-fog-width);
      height: 1.5em;
      background: linear-gradient(90deg, transparent, var(--cursor-fog-bg-color));
      position: absolute;
      margin-left: calc(-1 * var(--cursor-fog-width));
    }
  }
}

/* 修改光标样式 */
.typer-content.typing-cursor::after {
  content: var(--cursor-char);
  margin-left: 2px;
  display: inline-block;
  /* 确保光标对齐 */
}

// 新增 雾化样式
.typer-content.typing-cursor-foggy {
  position: relative;
  overflow: hidden;

  &:last-child:after {
    content: '';
    width: var(--cursor-fog-width);
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--cursor-fog-bg-color));
    position: absolute;
    margin-left: calc(-1 * var(--cursor-fog-width));
  }
}
