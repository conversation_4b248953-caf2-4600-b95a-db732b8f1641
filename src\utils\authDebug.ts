/**
 * 认证调试工具
 * 
 * 用于调试前端token认证问题
 */
import { Session } from '@/utils/storage';
import Cookies from 'js-cookie';

export interface TokenInfo {
  source: string;
  value: string;
  hasBearer: boolean;
  isValid: boolean;
  length: number;
}

export interface AuthDebugInfo {
  tokens: TokenInfo[];
  recommendations: string[];
  currentToken: string | null;
  isLoggedIn: boolean;
}

/**
 * 认证调试器类
 */
export class AuthDebugger {
  
  /**
   * 获取所有可能的token来源
   */
  static getAllTokens(): TokenInfo[] {
    const tokens: TokenInfo[] = [];
    
    // 1. 从Session获取token
    try {
      const sessionToken = Session.get('token');
      if (sessionToken) {
        tokens.push({
          source: 'Session Storage',
          value: sessionToken,
          hasBearer: sessionToken.startsWith('Bearer '),
          isValid: this.isTokenValid(sessionToken),
          length: sessionToken.length
        });
      }
    } catch (error) {
      console.warn('获取Session token失败:', error);
    }
    
    // 2. 从Cookies获取token
    try {
      const cookieToken = Cookies.get('token');
      if (cookieToken) {
        tokens.push({
          source: 'Cookies',
          value: cookieToken,
          hasBearer: cookieToken.startsWith('Bearer '),
          isValid: this.isTokenValid(cookieToken),
          length: cookieToken.length
        });
      }
    } catch (error) {
      console.warn('获取Cookie token失败:', error);
    }
    
    // 3. 从localStorage获取token
    try {
      const localToken = localStorage.getItem('token');
      if (localToken) {
        tokens.push({
          source: 'Local Storage',
          value: localToken,
          hasBearer: localToken.startsWith('Bearer '),
          isValid: this.isTokenValid(localToken),
          length: localToken.length
        });
      }
    } catch (error) {
      console.warn('获取localStorage token失败:', error);
    }
    
    // 4. 从sessionStorage获取token
    try {
      const sessionStorageToken = sessionStorage.getItem('token');
      if (sessionStorageToken) {
        tokens.push({
          source: 'Session Storage (Direct)',
          value: sessionStorageToken,
          hasBearer: sessionStorageToken.startsWith('Bearer '),
          isValid: this.isTokenValid(sessionStorageToken),
          length: sessionStorageToken.length
        });
      }
    } catch (error) {
      console.warn('获取sessionStorage token失败:', error);
    }
    
    return tokens;
  }
  
  /**
   * 简单验证token格式
   */
  static isTokenValid(token: string): boolean {
    if (!token || token.trim().length === 0) {
      return false;
    }
    
    // 移除Bearer前缀进行验证
    const cleanToken = token.replace(/^Bearer\s+/, '');
    
    // 基本长度检查
    if (cleanToken.length < 10) {
      return false;
    }
    
    // JWT格式检查（三个部分用.分隔）
    const parts = cleanToken.split('.');
    if (parts.length === 3) {
      // 可能是JWT token
      return true;
    }
    
    // UUID格式检查（Java系统可能使用UUID）
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(cleanToken)) {
      return true;
    }
    
    // 其他格式的token（长度大于20认为可能有效）
    return cleanToken.length > 20;
  }
  
  /**
   * 获取当前使用的token
   */
  static getCurrentToken(): string | null {
    return Session.get('token') || null;
  }
  
  /**
   * 格式化token用于显示
   */
  static formatTokenForDisplay(token: string): string {
    if (!token) return 'N/A';
    
    if (token.length > 50) {
      return `${token.substring(0, 20)}...${token.substring(token.length - 10)}`;
    }
    return token;
  }
  
  /**
   * 生成认证调试信息
   */
  static getDebugInfo(): AuthDebugInfo {
    const tokens = this.getAllTokens();
    const currentToken = this.getCurrentToken();
    const isLoggedIn = !!currentToken;
    
    const recommendations: string[] = [];
    
    // 生成建议
    if (tokens.length === 0) {
      recommendations.push('❌ 未找到任何token，请先登录');
      recommendations.push('💡 检查登录流程是否正常');
    } else {
      const validTokens = tokens.filter(t => t.isValid);
      if (validTokens.length === 0) {
        recommendations.push('❌ 找到token但格式无效');
        recommendations.push('💡 检查token是否被正确存储');
      } else {
        recommendations.push(`✅ 找到 ${validTokens.length} 个有效token`);
        
        // 检查Bearer前缀
        const withoutBearer = validTokens.filter(t => !t.hasBearer);
        if (withoutBearer.length > 0) {
          recommendations.push('⚠️ 部分token缺少Bearer前缀');
          recommendations.push('💡 API调用时会自动添加Bearer前缀');
        }
        
        // 检查token一致性
        const uniqueTokens = new Set(validTokens.map(t => t.value.replace(/^Bearer\s+/, '')));
        if (uniqueTokens.size > 1) {
          recommendations.push('⚠️ 发现多个不同的token');
          recommendations.push('💡 建议清除缓存后重新登录');
        }
      }
    }
    
    if (!isLoggedIn) {
      recommendations.push('🔑 请先登录获取有效token');
    }
    
    return {
      tokens,
      recommendations,
      currentToken,
      isLoggedIn
    };
  }
  
  /**
   * 在控制台打印调试信息
   */
  static printDebugInfo(): void {
    const debugInfo = this.getDebugInfo();
    
    console.group('🔍 认证调试信息');
    
    console.log('📊 Token统计:');
    console.log(`  - 总数: ${debugInfo.tokens.length}`);
    console.log(`  - 有效: ${debugInfo.tokens.filter(t => t.isValid).length}`);
    console.log(`  - 当前登录状态: ${debugInfo.isLoggedIn ? '✅ 已登录' : '❌ 未登录'}`);
    
    if (debugInfo.tokens.length > 0) {
      console.log('\n📋 Token详情:');
      debugInfo.tokens.forEach((token, index) => {
        console.log(`  ${index + 1}. ${token.source}:`);
        console.log(`     值: ${this.formatTokenForDisplay(token.value)}`);
        console.log(`     长度: ${token.length}`);
        console.log(`     Bearer前缀: ${token.hasBearer ? '✅' : '❌'}`);
        console.log(`     格式有效: ${token.isValid ? '✅' : '❌'}`);
      });
    }
    
    if (debugInfo.currentToken) {
      console.log('\n🎯 当前使用的token:');
      console.log(`  ${this.formatTokenForDisplay(debugInfo.currentToken)}`);
    }
    
    console.log('\n💡 建议:');
    debugInfo.recommendations.forEach(rec => {
      console.log(`  ${rec}`);
    });
    
    console.groupEnd();
  }
  
  /**
   * 清除所有token
   */
  static clearAllTokens(): void {
    try {
      Session.remove('token');
      Cookies.remove('token');
      localStorage.removeItem('token');
      sessionStorage.removeItem('token');
      console.log('✅ 已清除所有token');
    } catch (error) {
      console.error('❌ 清除token失败:', error);
    }
  }
  
  /**
   * 测试token是否可用
   */
  static async testToken(token?: string): Promise<boolean> {
    const testToken = token || this.getCurrentToken();
    if (!testToken) {
      console.log('❌ 没有token可测试');
      return false;
    }
    
    try {
      // 使用健康检查API测试token
      const response = await fetch('/api/iot/v1/ai/health', {
        method: 'GET',
        headers: {
          'Authorization': testToken.startsWith('Bearer ') ? testToken : `Bearer ${testToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        console.log('✅ Token测试成功');
        return true;
      } else {
        console.log(`❌ Token测试失败: ${response.status} ${response.statusText}`);
        return false;
      }
    } catch (error) {
      console.log('❌ Token测试异常:', error);
      return false;
    }
  }
}

// 全局暴露调试器（开发环境）
if (import.meta.env.DEV) {
  (window as any).authDebugger = AuthDebugger;
  console.log('🔧 认证调试器已加载，使用 authDebugger.printDebugInfo() 查看详情');
}

export default AuthDebugger;
