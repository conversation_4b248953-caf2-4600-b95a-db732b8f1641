.el-thinking {
  font-family: system-ui, sans-serif;
  margin: 0 auto;
}

.trigger {
  display: flex;
  align-items: center;
  height: 100%;
  width: var(--el-thinking-button-width);
  gap: 8px;
  padding: var(--el-padding-sm, 12px) calc(var(--el-padding-sm, 12px) + 4px);
  border: 1px solid #e4e4e4;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  margin-bottom: 8px;

  /* 居中 */
  .el-icon-center {
    height: 100%;
    display: flex;
    align-items: center;
  }

  /* 开始颜色 */
  .start-color {
    color: var(--el-color-warning);
  }

  /* 完成颜色 */
  .end-color {
    color: var(--el-color-success);
  }

  /* 思考中颜色 */
  .is-loading {
    color: var(--el-color-primary);
  }

  /* 思考失败颜色 */
  .error-color {
    color: var(--el-color-danger);
  }
}

.trigger:hover {
  background: #f8f8f8;
}

.trigger.disabled {
  cursor: pointer;
}

.trigger:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.status-icon {
  font-size: 1.2em;
}

.arrow {
  margin-left: auto;
  transition: transform var(--el-thinking-animation-duration);
}

.arrow.expanded {
  transform: rotate(180deg);
}

/* 滑动动画 */
.slide-enter-active,
.slide-leave-active {
  height: calc-size(max-content, size);
  transition:
    height var(--el-thinking-animation-duration) ease-in-out,
    opacity var(--el-thinking-animation-duration) ease-in-out;
  overflow: hidden;
}

.slide-enter-from,
.slide-leave-to {
  height: 0 !important;
  opacity: 0;
}

/* 内容区域样式 */
.content-wrapper {
  box-sizing: border-box;
  min-width: 0;
}

.content pre {
  border: 1px solid #eee;
  background: var(--el-thinking-content-wrapper-background-color);
  padding: var(--el-padding-sm, 12px) calc(var(--el-padding-sm, 12px) + 4px);
  border-radius: calc(var(--el-border-radius-base) + 4px);
  max-width: var(--el-thinking-content-wrapper-width);
  font-size: 14px;
  color: var(--el-thinking-content-wrapper-color);
  white-space: pre-wrap;
  font-family:
    DeepSeek-CJK-patch,
    Inter,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    Segoe UI,
    Roboto,
    Noto Sans,
    Ubuntu,
    Cantarell,
    Helvetica Neue,
    Oxygen,
    Open Sans,
    sans-serif;
  margin: 0;
  line-height: var(--el-font-line-height-primary);
}

.error-state {
  border-color: #ffd0d0;
  background: #fff0f0;
}

.error-message {
  color: #dc3545;
  height: fit-content;
  padding: 8px;
  background: #ffeef0;
  border-radius: 4px;
}
