.el-thought-chain {
  &-item-dot {
    margin: v-bind(dotMargin);
    :deep(.el-button) {
      cursor: default !important;
      &:active {
        background-color: var(--custom-background-color) !important;
        border-color: var(--custom-border-color) !important;
      }
      &:hover {
        background-color: var(--custom-background-color) !important;
        border-color: var(--custom-border-color) !important;
      }
      .el-icon svg path {
        fill: white;
      }
    }
  }

  :deep(.el-collapse) {
    border: none;

    .el-collapse-item__header {
      height: 20px;
      font-weight: normal;
    }

    .el-collapse-item__arrow {
      margin: 0 0 0 8px;
    }

    .el-collapse-item__header {
      margin-bottom: 5px;
    }

    .el-collapse-item__header,
    .el-collapse-item__wrap {
      border: none;
    }

    .el-collapse-item__content {
      color: var(--el-text-color-secondary);
      padding: 0;
    }
  }

  :deep(.el-timeline) {
    padding: 10px 0 0 5px;
  }

  :deep(.el-timeline-item__timestamp) {
    color: var(--el-text-color-primary);
  }

  :deep(.el-timeline-item__content) {
    color: var(--el-text-color-secondary);
  }
}

.thought-chain-move,
.thought-chain-enter-active,
.thought-chain-leave-active {
  transition: all 0.5s ease;
}

.thought-chain-enter-from,
.thought-chain-leave-to {
  opacity: 0;
  transform: translateY(10px) scaleY(0.9);
}

.thought-chain-leave-active {
  position: absolute;
}

.thought-chain-loading {
  animation: rotating 1.5s linear infinite;
  transform-origin: center center;
  will-change: transform;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(90deg);
  }
  50% {
    transform: rotate(180deg);
  }
  75% {
    transform: rotate(270deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
