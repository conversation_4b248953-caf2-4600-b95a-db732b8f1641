<script setup lang="ts">
import * as echarts from 'echarts';
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { View, CopyDocument } from '@element-plus/icons-vue';

// 保留原有props.code逻辑，同时添加可选配置
const props = defineProps<{
  code: string; // 原始JSON字符串配置
  width?: string; // 可选：图表宽度
  height?: string; // 可选：图表高度
  theme?: string; // 可选：图表主题
}>();

const refEle = ref<HTMLElement>();
let myChart: echarts.ECharts | null = null; // 图表实例引用

// 查看代码功能
const showCode = ref(false);

// 复制代码功能
const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(props.code);
    ElMessage.success('代码已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败');
  }
};

function parseEChartsOption(str: string): any {
  try {
    // Step 1: Remove variable assignment prefix and trailing semicolon
    let cleanedStr = str.replace(/^option\s*=\s*/, '').replace(/;\s*$/, '');

    // Step 2: Replace single quotes with double quotes for string values
    cleanedStr = cleanedStr.replace(/'/g, '"');

    // Step 3: Add double quotes around unquoted keys
    cleanedStr = cleanedStr.replace(/(\w+)\s*:/g, '"$1":');

    // Step 4: Parse the transformed string into JSON object
    return JSON.parse(cleanedStr);
  }
  catch (error) {
    console.error('Failed to parse ECharts option:', error);
    return null;
  }
}

// 核心渲染逻辑（保留原始解析流程）
function renderChart() {
  if (!refEle.value)
    return;

  try {
    // 解析JSON配置（保留原有逻辑）
    const cleanedStr = parseEChartsOption(props.code);
    
    if (!cleanedStr) {
      console.error('ECharts配置解析失败');
      return;
    }

    // 初始化/更新图表
    if (!myChart) {
      myChart = echarts.init(refEle.value, props.theme || 'light');
    }
    myChart.setOption(cleanedStr);
  }
  catch (error) {
    console.error('图表配置解析失败:', error);
  }
}

// 窗口resize处理
function handleResize() {
  myChart?.resize();
}

// 销毁逻辑
function destroyChart() {
  if (myChart) {
    myChart.dispose(); // 释放ECharts实例
    myChart = null;
  }
  window.removeEventListener('resize', handleResize);
}

// 初始化渲染
onMounted(() => {
  renderChart();
  window.addEventListener('resize', handleResize); // 添加resize监听
});

// 监听code变化自动更新（关键优化）
watch(
  () => props.code,
  () => {
    renderChart(); // 配置变化时重新渲染
  }
);

// 卸载时清理资源
onUnmounted(() => {
  destroyChart();
});
</script>

<template>
  <div class="echarts-wrap">
    <!-- 工具栏 -->
    <div class="echarts-toolbar">
      <div class="echarts-title">📊 ECharts 图表</div>
      <div class="echarts-actions">
        <el-button
          size="small"
          type="primary"
          :icon="View"
          @click="showCode = !showCode"
        >
          {{ showCode ? '隐藏代码' : '查看代码' }}
        </el-button>
        <el-button
          size="small"
          :icon="CopyDocument"
          @click="copyCode"
        >
          复制
        </el-button>
      </div>
    </div>

    <!-- 图表容器 -->
    <div
      ref="refEle"
      class="echarts-chart-container"
      :style="{
        height: height || '400px',
        width: width || '100%'
      }"
    />

    <!-- 代码展示区域 -->
    <el-collapse-transition>
      <div v-show="showCode" class="code-viewer">
        <div class="code-header">
          <span>📝 ECharts 配置代码</span>
        </div>
        <pre class="code-content"><code>{{ code }}</code></pre>
      </div>
    </el-collapse-transition>
  </div>
</template>

<style scoped>
.echarts-wrap {
  position: relative;
  background: white;
  border-radius: 6px;
  border: 1px solid var(--el-border-color-light);
  margin: 8px 0 32px 0; /* 增加底部间距，避免与后续内容太近 */
  overflow: hidden;
}

.echarts-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color-light);
  gap: 20px; /* 增加标题和按钮之间的最小间距 */
}

.echarts-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  flex: 1; /* 让标题占据剩余空间 */
  margin-right: 16px; /* 确保与按钮有足够间距 */
}

.echarts-actions {
  display: flex;
  gap: 12px; /* 增加按钮之间的间距 */
}

.echarts-chart-container {
  padding: 16px;
  margin-bottom: 20px; /* 增加图表底部间距 */
}

.code-viewer {
  background: var(--el-fill-color-extra-light);
  border-top: 1px solid var(--el-border-color-light);
  margin-top: 8px;
}

.code-header {
  padding: 12px 16px;
  background: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color-light);
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.code-content {
  background: var(--el-bg-color-page);
  padding: 16px;
  margin: 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: var(--el-text-color-primary);
  max-height: 400px;
  overflow-y: auto;
}
</style>
